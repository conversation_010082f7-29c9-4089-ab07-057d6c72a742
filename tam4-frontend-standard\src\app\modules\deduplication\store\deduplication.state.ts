import { ClientErrors, RelationshipValidateDraft } from "src/app/modules/layout/store/actions/popup.actions";
import { ObjectsUtils } from "src/app/utils";
import { RelationshipType } from "../../relationships/endpoint/relationships.model";
import {
    ClientStatuses,
    CreateDeduplicationRequest, DeduplicationBasicDataRowDto, DeduplicationEnrichedDataDetails, DeduplicationFieldConfiguration,
    DeduplicationMaterialData,
    DeduplicationStep, LicenseConfigurations,
    Subtype,
    SubtypesRelationships
} from '../models/deduplication.models';

export const TAM_DEDUPLICATION_FEATURE_NAME = 'deduplication-management';

export interface DeduplicationState {
    loading: boolean;
    materialIds: string[];
    groupMaterials: DeduplicationMaterialData;
    note: string;
    isLoading: boolean;
    crossClient: boolean;
    errorMessage: string;
    errorRelationshipMessage: string;
    isSubmittingData: boolean;
    isValid: boolean;
    relationshipValidateDrafts: RelationshipValidateDraft[];
    clientErrors?: ClientErrors;
    availableStatusesByClient?: ClientStatuses;
    availableStatusesByClientLoading?: boolean;
    subtypesRelationships?: SubtypesRelationships[];
    selectedSubtypes?: Subtype;
    deduplicationRequest?: CreateDeduplicationRequest;
    stepper: DeduplicationStep[];
    step: DeduplicationStep;
    licenseConfigurations: LicenseConfigurations;
    enrichmentMaterialDetails?: DeduplicationEnrichedDataDetails[];
    enrichedMaterialsData?: { [client: string]: { [rowId: string]: { [fieldKey: string]: DeduplicationFieldConfiguration } } };
    groupClients?: string[];
    currentClient?: string;
}

export const TAM_DEDUPLICATION_INITIAL_STATE: DeduplicationState = ObjectsUtils.deepClone({
    loading: false,
    note: null,
    materialIds: [],
    isSubmittingData: false,
    crossClient: false,
    groupMaterials: {
        materials: [],
    },
    isLoading: false,
    errorMessage: null,
    errorRelationshipMessage: null,
    relationshipValidateDrafts: [],
    isValid: false,
    deduplicationRequest: {
        relationshipType: RelationshipType.DUPLICATE,
        creatingGoldenRecord: false,
        materialsInRelationship: [],
        materialsDeduplication: []
    },
    step: null,
    licenseConfigurations: {
        deduplicationBasicDataEnrichment: false, // TODO: cambiare in deduplicationMaterialEnrichment
        deduplicationPlantEnrichment: false,
        deduplicationSubTypesEnabled: false,
        deduplicationMasterDataStatusEnabled: false,
        deduplicationMasterDataStatusRequired: false
    },
    stepper: []
});
