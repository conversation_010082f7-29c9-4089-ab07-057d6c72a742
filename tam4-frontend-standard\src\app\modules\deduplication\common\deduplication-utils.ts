import {
    CreateDeduplicationRequest,
    DeduplicationBasicDataRowDto, DeduplicationFieldConfiguration,
    DeduplicationStep,
    RelationshipMaterialInfo,
    StepPage
} from '../models/deduplication.models';
import {RelationshipTypes} from '../../layout/store/actions/popup.actions';

// export function isValidField(step: DeduplicationStep, request: CreateDeduplicationRequest,  materials: RelationshipMaterialInfo[]): boolean {
//   let isValid = true;
//   switch (step.stepPage) {
//       case StepPage.RELATIONSHIP_TYPE:
//           return validateRelationshipType(request);
//       case StepPage.MATERIALS_SELECTION:
//           return materials && materials.filter((m: RelationshipMaterialInfo) => m.selected).length >= 2;
//       case StepPage.MATERIAL_ENRICHMENT:
//           break;
//       case StepPage.PLANT_DATA_ENRICHMENT:
//           break;
//       default:
//           console.warn('can\'t use this type as intended!', step, request);
//   }
//   return isValid;
// }
//
// function validateRelationshipType(request: CreateDeduplicationRequest): boolean {
//     // TODO: Aggiungere controllo per le relazioni che non hanno i subtypes
//     return !!request?.relationshipType && !!request?.subtype;
// }

export function getRole(
    it: RelationshipMaterialInfo,
    type: string
) {
    const relationshipType = relationshipTypeFromString(type.toLowerCase()); // TODO: Check this!!!
    switch (relationshipType) {
        case RelationshipTypes.equivalence:
            return 'EQUIVALENT';
        case RelationshipTypes.interchangeable:
            return it.primary ? 'CAN_SUBSTITUTE' : 'CAN_BE_SUBSTITUTED_BY';
        case RelationshipTypes.duplicate:
            return it.primary ? 'PRIMARY' : 'SECONDARY';
        case RelationshipTypes.noRelationship:
            return 'NO_RELATIONSHIP';
    }
    throw new Error();
}

// TODO: Simplify this function
export function relationshipTypeFromString(type: string): RelationshipTypes {
    switch (type) {
        case 'equivalence':
            return RelationshipTypes.equivalence;
        case 'interchangeable':
            return RelationshipTypes.interchangeable;
        case 'duplicate':
            return RelationshipTypes.duplicate;
        case 'noRelationship':
            return RelationshipTypes.noRelationship;
        default:
            throw new Error(`Unsupported relationship type: ${type}`);
    }
}

export function applySecondarySelection(
    row: DeduplicationBasicDataRowDto,
    selectedSecondary: DeduplicationFieldConfiguration | undefined,
    selectedMaterialKey: string,
    toggle: boolean,
    enrichedData: { [rowId: string]: { [fieldKey: string]: DeduplicationFieldConfiguration } }
): void {
    if (!selectedSecondary?.editable || !selectedSecondary.value || selectedSecondary.value === '') {
        return;
    }

    if (toggle) {
        selectedSecondary.selected = selectedSecondary.selected === undefined ? true : !selectedSecondary.selected;
    } else {
        selectedSecondary.selected = true;
    }

    const primaryKeys = Object.keys(row.primary || {});
    const primaryKey = primaryKeys.length > 0 ? primaryKeys[0] : null;
    const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

    if (!primaryField) {
        console.warn(`Primary field not found for material key: ${selectedMaterialKey}`);
        return;
    }

    if (!primaryKey) {
        console.warn(`Primary key not found for material key: ${selectedMaterialKey}`);
        return;
    }

    const enrichedRow = enrichedData[row.id];
    if (!enrichedRow || !enrichedRow[primaryKey]) {
        console.warn(`Enriched data not found for rowId=${row.id}, primaryKey=${primaryKey}`);
        return;
    }

    const enrichedPrimaryField = enrichedRow[primaryKey];

    if (selectedSecondary.selected) {
        Object.entries(row.secondaries).forEach(([materialKey, secondary]) => {
            if (
                materialKey !== selectedMaterialKey &&
                secondary &&
                secondary.editable &&
                secondary.selected &&
                secondary.id === selectedSecondary.id
            ) {
                secondary.selected = false;
            }
        });

        if (enrichedPrimaryField.oldValue === undefined) {
            enrichedPrimaryField.oldValue = enrichedPrimaryField.value;
        }
        enrichedPrimaryField.value = `${selectedSecondary.value}_${selectedMaterialKey}`;
        primaryField.value = selectedSecondary.value + "_" + selectedMaterialKey;
        primaryField.edited = enrichedPrimaryField.oldValue !== enrichedPrimaryField.value;
    } else {
        if (enrichedPrimaryField.oldValue !== undefined) {
            enrichedPrimaryField.value = enrichedPrimaryField.oldValue;
            primaryField.value = enrichedPrimaryField.oldValue;
            primaryField.edited = enrichedPrimaryField.oldValue !== enrichedPrimaryField.value;
            delete enrichedPrimaryField.oldValue;
        }
    }
}


