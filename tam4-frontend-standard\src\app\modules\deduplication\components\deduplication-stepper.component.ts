import { CommonModule } from "@angular/common";
import { Component, inject, Input, OnInit, Signal, computed, effect } from "@angular/core";
import { SelectorMap } from "@creactives/models";
import { Tam4TranslationService } from "@creactives/tam4-translation-core";
import { Store } from "@ngrx/store";
import { TranslateService } from "@ngx-translate/core";
import { Tam4SelectorConfig, TamAbstractReduxComponent } from "src/app/components";
import { DeduplicationService } from "../services/deduplication.service";
import { DeduplicationSelectors } from "../store/deduplication.selectors";
import {SubtypesRelationships, RelationshipMaterialInfo, CreateDeduplicationRequest, LicenseConfigurations} from '../models/deduplication.models';
import { RelationshipMaterialEnrich } from "./wizard/steps/relationship-material-enrich.component";
import { RelationshipPlantDataEnrich } from "./wizard/steps/relationship-plant-data-enrich.component";
import { RelationshipTypesListComponent } from "./wizard/steps/relationship-types-list.component";
import { RelationshipWizardStep2Component } from "./wizard/steps/relationship-wizard-md-selection.component";
import { RelationshipMaterialStatusComponent } from "./wizard/steps/relationship-material-status.component";
import { DeduplicationSummaryComponent } from "./wizard/steps/deduplication-summary.component";
import { DeduplicationStepFactoryService, StepConfiguration, StepperConfiguration } from "../services/deduplication-step-factory.service";

import {StepperModule} from 'primeng/stepper';
import {ButtonModule} from 'primeng/button';
import { TranslateModule } from '@ngx-translate/core';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'isLoading', selector: DeduplicationSelectors.getIsLoading},
  {key: 'hasError', selector: DeduplicationSelectors.getHasError},
  {key: 'stepNumber', selector: DeduplicationSelectors.getStepNumber},
  {key: 'subtypesRelationships', selector: DeduplicationSelectors.getSubtypesRelationships},
  {key: 'selectedSubtypesRelationships', selector: DeduplicationSelectors.getSelectedSubtypesRelationships},
  {key: 'deduplicationRequest', selector: DeduplicationSelectors.getDeduplicationRequest},
  {key: 'relationshipType', selector: DeduplicationSelectors.getRelationshipType},
  {key: 'materials', selector: DeduplicationSelectors.getMaterials},
  {key: 'isCreatingGoldenRecord', selector: DeduplicationSelectors.getIsCreatingGoldenRecord},
  {key: 'clientErrors', selector: DeduplicationSelectors.getClientErrors},
  {key: 'relationshipValidateDraft', selector: DeduplicationSelectors.getRelationshipValidateDraft},
  {key: 'licenseConfigurations', selector: DeduplicationSelectors.getLicenseConfiguration}
];

@Component({
  selector: 'deduplication-stepper',
  template: `
<!--    <pre>{{ enabledSteps() | json }}</pre>-->
    <p-stepper [linear]="true" [activeStep]="signals?.stepNumber() || 0" (activeStepChange)="onStepChange($event)">
      @for (step of enabledSteps(); track step.stepType + '-' + step.stepPage) {
        <p-stepperPanel [header]="step.headerKey | translate">
          <ng-template pTemplate="header" let-onClick="onClick" let-index="index">
            <button class="bg-transparent border-none inline-flex flex-column gap-2">
              <span class="border-round border-2 w-3rem h-3rem inline-flex align-items-center justify-content-center">
                <i [class]="step.icon"></i>
              </span>
            </button>
          </ng-template>
          <ng-template pTemplate="content" let-nextCallback="nextCallback" let-prevCallback="prevCallback" let-index="index">
            <div class="m-form">
              <ng-container [ngSwitch]="step.stepType">
                <!-- Relationship Types Step -->
                <relationship-types-list
                  *ngSwitchCase="'relationship-types-list'"
                  [mdDomain]="mdDomain"
                  [subtypesRelationships]="subtypesRelationships"
                  [deduplicationRequest]="deduplicationRequest">
                </relationship-types-list>

                <!-- Material Selection Step -->
                <relationship-wizard-md-selection
                  *ngSwitchCase="'relationship-wizard-md-selection'"
                  [mdDomain]="mdDomain">
                </relationship-wizard-md-selection>

                <!-- Material Status Step -->
                <relationship-material-status
                    *ngSwitchCase="'relationship-material-status'"
                    [mdDomain]="mdDomain">
                </relationship-material-status>

                <!-- Material Enrichment Step -->
                <relationship-material-enrich
                  *ngSwitchCase="'relationship-material-enrich'"
                  [mdDomain]="mdDomain">
                </relationship-material-enrich>

                <!-- Plant Data Enrichment Step -->
                <relationship-plant-data-enrich
                  *ngSwitchCase="'relationship-plant-data-enrich'"
                  [mdDomain]="mdDomain">
                </relationship-plant-data-enrich>

                <!-- Summary Step -->
                <deduplication-summary
                  *ngSwitchCase="'deduplication-summary'"
                  [mdDomain]="mdDomain">
                </deduplication-summary>
              </ng-container>

              @if (hasStepErrors(index)) {
                <div class="alert alert-danger mt-3">
                  @for (error of getStepValidationErrors(index); track error) {
                    <div>{{ error }}</div>
                  }
                </div>
              }
            </div>
          </ng-template>
        </p-stepperPanel>
      }
    </p-stepper>
  `,
  styles: [``],
  standalone: true,
  imports: [
    CommonModule,
    StepperModule,
    ButtonModule,
    TranslateModule,
    RelationshipTypesListComponent,
    RelationshipWizardStep2Component,
    RelationshipMaterialEnrich,
    RelationshipPlantDataEnrich,
    RelationshipMaterialStatusComponent,
    DeduplicationSummaryComponent
  ]
})
export class DeduplicationStepperComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit {

  @Input() mdDomain: string;
  @Input() subtypesRelationships: Signal<SubtypesRelationships[] | []>;
  @Input() deduplicationRequest: Signal<CreateDeduplicationRequest | null>;

  service = inject(DeduplicationService);
  stepFactoryService = inject(DeduplicationStepFactoryService);

  // visibleSteps = computed(() => {
  //   const licenseConfig = this.signals?.licenseConfigurations();
  //   const isCreatingGoldenRecord = this.signals?.isCreatingGoldenRecord() || false;
  //
  //   if (!licenseConfig) {
  //     return [];
  //   }
  //
  //   return this.stepFactoryService.getVisibleSteps(licenseConfig, isCreatingGoldenRecord);
  // });

  enabledSteps = computed(() => {
    const licenseConfig = this.signals?.licenseConfigurations();
    const isCreatingGoldenRecord = this.signals?.isCreatingGoldenRecord() || false;

    if (!licenseConfig) {
      return [];
    }

    console.log('Enabled Steps:', licenseConfig, isCreatingGoldenRecord);

    return this.stepFactoryService.getEnabledSteps(licenseConfig, isCreatingGoldenRecord);
  });

  constructor(
    protected translate: TranslateService,
    protected tamTranslate: Tam4TranslationService,
    protected store: Store
  ) {
    super(translate, tamTranslate, store, storeSelectors);

    effect(() => {
      const currentStepIndex = this.signals?.stepNumber() || 0;
      const visibleSteps = this.enabledSteps();

      if (currentStepIndex >= visibleSteps.length) {
        const lastStepIndex = Math.max(0, visibleSteps.length - 1);
        this.navigateToStep(lastStepIndex);
      }
    }, { allowSignalWrites: true });
  }

  ngOnInit() {

    // this.store.select(DeduplicationSelectors.getIsCreatingGoldenRecord).subscribe(isGoldenRecord => {
    //   this.configureSteps(this.licenseParameters, isGoldenRecord);
    // });
  }

  isStepEnabled(step: StepConfiguration): boolean {
    const licenseConfig = this.signals?.licenseConfigurations();
    const isCreatingGoldenRecord = this.signals?.isCreatingGoldenRecord() || false;

    if (!licenseConfig) {
      return false;
    }

    return step.isEnabled(licenseConfig, isCreatingGoldenRecord);
  }

  // onStepChange_(event: any) {
  //   // Handle step change events from the stepper
  //   const targetStep = event.index;
  //
  //   // Allow navigation if validation passes
  //   if (this.canNavigateToStep(targetStep)) {
  //     // Update the step in the store
  //     this.service.action_doSetStepPage(targetStep);
  //   } else {
  //     // Prevent navigation by reverting to current step
  //     event.preventDefault();
  //   }
  // }

  onStepChange(event: any) {
    // Handle step change events from the stepper
    const targetStepIndex = event.index;
    const currentStepIndex = this.signals?.stepNumber() || 0;

    // Always allow backward navigation
    if (targetStepIndex < currentStepIndex) {
      this.navigateToStep(targetStepIndex);
      return;
    }

    // For forward navigation, validate current step
    if (targetStepIndex > currentStepIndex) {
      if (this.canNavigateToStep(targetStepIndex)) {
        this.navigateToStep(targetStepIndex);
      } else {
        // Prevent navigation by reverting to current step
        event.preventDefault();
      }
    }
  }

  private navigateToStep(stepIndex: number) {
    const visibleSteps = this.enabledSteps();
    if (stepIndex >= 0 && stepIndex < visibleSteps.length) {
      const targetStep = visibleSteps[stepIndex];
      this.service.action_doSetStepPage(targetStep.stepPage);
    }
  }

  private canNavigateToStep(targetStepIndex: number): boolean {
    const currentStepIndex = this.signals?.stepNumber() || 0;
    const visibleSteps = this.enabledSteps();

    // Check if target step index is valid
    if (targetStepIndex < 0 || targetStepIndex >= visibleSteps.length) {
      return false;
    }

    // Allow navigation backwards
    if (targetStepIndex < currentStepIndex) {
      return true;
    }

    // Allow navigation forward only if current step is valid and target is next step
    if (targetStepIndex === currentStepIndex + 1) {
      return !this.hasStepErrors(currentStepIndex);
    }

    return false;
  }


  // goToNextStep() {
  //   const currentStep = this.signals?.deduplicationRequest()?.stepPage || 0;
  //
  //   // Validate current step before proceeding
  //   if (this.validateCurrentStep(currentStep)) {
  //     this.service.action_doGoToNextStep();
  //   }
  // }

  // goToPreviousStep() {
  //   this.service.action_doGoToPreviousStep();
  // }

  requestCreation() {
    // This will be handled by the parent component through an output event
    // For now, we'll call the service method directly
    // In a real implementation, this should emit an event to the parent
  }

  // private validateCurrentStep(stepNumber: number): boolean {
  //   switch (stepNumber) {
  //     case 0: // Relationship Type Selection
  //       return !!this.signals?.deduplicationRequest()?.relationshipType;
  //     case 1: // Material Selection
  //       const materials = this.signals?.materials();
  //       return materials && materials.filter((m: RelationshipMaterialInfo) => m.selected).length >= 2;
  //     case 2: // Material Enrichment
  //       return true; // Add specific validation logic if needed
  //     case 3: // Plant Data Enrichment
  //       return true; // Add specific validation logic if needed
  //     case 4: // Review
  //       return true; // Final validation before creation
  //     default:
  //       return true;
  //   }
  // }

  adaptMdDomain(mdDomain: string): string {
    // Implement the same logic as in NavigationStepsComponent
    return mdDomain?.toLowerCase() || '';
  }

  // // Helper method to check if step navigation should be allowed
  // canNavigateToStep(targetStep: number): boolean {
  //   const currentStep = this.signals?.deduplicationRequest()?.stepPage || 0;
  //
  //   // Allow navigation backwards
  //   if (targetStep < currentStep) {
  //     return true;
  //   }
  //
  //   // Allow navigation forward only if current step is valid
  //   if (targetStep === currentStep + 1) {
  //     return this.validateCurrentStep(currentStep);
  //   }
  //
  //   return false;
  // }

  // Helper method to get validation errors for a specific step
  getStepValidationErrors(stepIndex: number): string[] {
    const errors: string[] = [];
    const visibleSteps = this.enabledSteps();

    if (stepIndex >= visibleSteps.length) {
      return errors;
    }

    const step = visibleSteps[stepIndex];

    switch (step.stepType) {
      case 'relationship-types-list':
        if (!this.signals?.deduplicationRequest()?.relationshipType) {
          errors.push(this.translate.instant('deduplication.validation.selectRelationshipType'));
        }
        break;
      case 'relationship-wizard-md-selection': // Material Selection
        const materials = this.signals?.materials();
        const selectedCount = materials ? materials.filter((m: RelationshipMaterialInfo) => m.selected).length : 0;
        if (selectedCount < 2) {
          errors.push(this.translate.instant('deduplication.validation.selectMinMaterials'));
        }
        // Add backend validation errors
        const backendErrors = this.signals?.relationshipValidateDraft();
        if (backendErrors && backendErrors.length > 0) {
          backendErrors.forEach((error: any) => {
            errors.push(error.errorMessage || this.translate.instant('deduplication.validation.genericError'));
          });
        }
        break;
      case 'relationship-material-enrich': // Material Enrichment
        // Add specific validation logic if needed
        break;
      case 'relationship-plant-data-enrich': // Plant Data Enrichment
        // Add specific validation logic if needed
        break;
      case 'relationship-material-status': // Material Status
        // Add specific validation logic if needed
        break;
      case 'deduplication-summary': // Summary
        // Final validation before creation
        break;
    }

    return errors;
  }

  hasStepErrors(stepIndex: number): boolean {
    return this.getStepValidationErrors(stepIndex).length > 0;
  }

  // Method to validate if a step can be enabled based on runtime conditions
  canStepBeEnabled(step: StepConfiguration): boolean {
    const licenseConfig = this.signals?.licenseConfigurations();
    const isCreatingGoldenRecord = this.signals?.isCreatingGoldenRecord() || false;

    if (!licenseConfig) {
      return false;
    }

    // Check if step is enabled based on license configuration and golden record state
    return step.isEnabled(licenseConfig, isCreatingGoldenRecord);
  }

  // Method to get the current step configuration
  getCurrentStepConfiguration(): StepConfiguration | null {
    const currentStepIndex = this.signals?.stepNumber() || 0;
    const visibleSteps = this.enabledSteps();

    if (currentStepIndex >= 0 && currentStepIndex < visibleSteps.length) {
      return visibleSteps[currentStepIndex];
    }

    return null;
  }

  // Method to check if we can proceed to the next step
  canProceedToNextStep(): boolean {
    const currentStepIndex = this.signals?.stepNumber() || 0;
    const visibleSteps = this.enabledSteps();

    // Check if there is a next step
    if (currentStepIndex >= visibleSteps.length - 1) {
      return false;
    }

    // Check if current step is valid
    return !this.hasStepErrors(currentStepIndex);
  }

}
