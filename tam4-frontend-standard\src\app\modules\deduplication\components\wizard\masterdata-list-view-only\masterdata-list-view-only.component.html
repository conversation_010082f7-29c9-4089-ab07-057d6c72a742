<div class="form-group col-md-12">
    <table class="table m-table">
        <thead>
        <tr>
            <th scope="col"></th>
            <th scope="col">{{'layout.relationship-popup.' + adaptMdDomain(mdDomain) + '-code' | translate}}</th>
            <th scope="col">{{'layout.relationship-popup.material-description' | translate}}</th>
            <th scope="col" *ngIf="(relationshipType$ | async)==='duplicate'" translate>layout.relationship-popup.md-statuses</th>
            <th scope="col" *ngIf="mdDomain != 'S'">{{'layout.relationship-popup.material-stock-amount' | translate}}</th>
            <th scope="col">{{'layout.relationship-popup.material-consumption-amount' | translate}}</th>
            <th scope="col">{{'layout.relationship-popup.material-order-amount' | translate}}</th>
            <th scope="col"></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let material of selectedMaterials$ | async; let materialIndex = index;">
            <td>
                <app-relationship-icon [material]="material"></app-relationship-icon>
            </td>
            <td style="width: 150px" (click)="openDetails(material)">
                <label class="open-detail" for="check-gr-mat_{{materialIndex}}">
                    #{{material.client}} / {{material.materialCode}}
                </label>
            </td>
            <td>
                <label for="check-gr-mat_{{materialIndex}}">
                    {{material.description}}
                </label>
            </td>
            <td *ngIf="(relationshipType$ | async)==='duplicate'" class="cell-larger">
                <label class="fit-width" for="check-gr-mat_{{materialIndex}}">
                    <select *ngIf="!material.primary"
                            [(ngModel)]="material.status"
                            (ngModelChange)="setMaterialStatus()"
                            class="form-control m-input"
                            [disabled]="loading"
                            autocomplete="off" data-cy="form.status">
                        <option *ngIf="loading">{{'deduplication.common.loading' | translate}}</option>
                        <option></option>
                        <option *ngFor="let opt of availableStatusesByClient[material.client]" [value]="opt.key">{{opt.text}}</option>
                    </select>
                </label>
            </td>
            <td *ngIf="mdDomain != 'S'">
                <label for="check-gr-mat_{{materialIndex}}" [ngClass]="{'highest':material.isTopStockValue, 'not-available':!material.stockAmount}">
                    {{material.stockAmount | number:'1.0-0':(language$ | async) || ('deduplication.common.notAvailable' | translate)}}
                </label>
            </td>
            <td>
                <label for="check-gr-mat_{{materialIndex}}" [ngClass]="{'highest':material.isTopConsumptionValue, 'not-available':!material.consumptionAmount}">
                    {{material.consumptionAmount | number:'1.0-0':(language$ | async) || ('deduplication.common.notAvailable' | translate)}}
                </label>
            </td>
            <td>
                <label for="check-gr-mat_{{materialIndex}}" [ngClass]="{'highest':material.isTopOrderedValue, 'not-available':!material.orderedAmount}">
                    {{material.orderedAmount | number:'1.0-0':(language$ | async) || ('deduplication.common.notAvailable' | translate)}}
                </label>
            </td>
            <td style="width: 50px">
                  <span *ngIf="material.completeness"
                        class="m-badge m-badge--danger m-badge--wide completeness completeness-{{material.completeness | lowercase}}">
                    {{material.completeness}}
                  </span>
            </td>
            <td style="width: 50px" *ngIf="!(isEquivalence$ | async)">
                <label class="fancy-checkbox">
                    <input id="check-gr-mat_{{materialIndex}}" type="checkbox" disabled
                           [checked]="material.primary"/>
                    <span></span>
                </label>
            </td>
            <td style="width: 50px" *ngIf="isEquivalence$ | async">
                <label class="m-checkbox m-checkbox--solid">
                    <input id="check-gr-mat_{{materialIndex}}" type="checkbox" disabled checked/>
                    <span></span>
                </label>
            </td>
        </tr>
        </tbody>
    </table>
    <div class="alert alert-solid-danger" role="alert" *ngIf="hasRelationshipError$ | async">
        <div class="alert-text">{{(errorRelationshipMessage$ | async)}}</div>
    </div>
</div>
