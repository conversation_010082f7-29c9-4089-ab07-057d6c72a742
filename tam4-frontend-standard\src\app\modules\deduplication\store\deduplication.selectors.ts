import { createFeatureSelector, createSelector } from "@ngrx/store";
import { DeduplicationState, TAM_DEDUPLICATION_FEATURE_NAME } from "./deduplication.state";

const getFullFeatureStateSelector = createFeatureSelector<DeduplicationState>(TAM_DEDUPLICATION_FEATURE_NAME);
const isLoadingSelector = createSelector(getFullFeatureStateSelector, state => state?.isLoading);
const hasErrorSelector = createSelector(getFullFeatureStateSelector, state => state?.errorMessage);
const getStepSelector = createSelector(getFullFeatureStateSelector, state => state?.step);
const getStepNumberSelector = createSelector(getFullFeatureStateSelector, state => state?.step?.stepPage ?? 0);
const getStepperSelector = createSelector(getFullFeatureStateSelector, state => state?.stepper);
const getRelationshipTypeSelector = createSelector(getFullFeatureStateSelector, state => state?.deduplicationRequest.relationshipType);
const getMaterialIdsSelector = createSelector(getFullFeatureStateSelector, state => state?.materialIds);
const getGroupMaterialsSelector = createSelector(getFullFeatureStateSelector, state => state?.groupMaterials);
const getMaterialsSelector = createSelector(getGroupMaterialsSelector, state => state?.materials);
const getRelationshipValidateDraftSelector = createSelector(getFullFeatureStateSelector, state => state?.relationshipValidateDrafts);
const getIsCreatingGoldenRecordSelector = createSelector(getFullFeatureStateSelector, state => state?.deduplicationRequest.creatingGoldenRecord || state?.deduplicationRequest.isCreatingGoldenRecord);
const getClientErrorsSelector = createSelector(getFullFeatureStateSelector, state => state?.clientErrors);
const getCrossClientSelector = createSelector(getFullFeatureStateSelector, state => state?.crossClient);
const getSubtypesRelationshipsSelector = createSelector(getFullFeatureStateSelector, state => state?.subtypesRelationships);
const getSelectedSubtypesRelationshipsSelector = createSelector(getFullFeatureStateSelector, state => state?.selectedSubtypes);
const getDeduplicationRequestSelector = createSelector(getFullFeatureStateSelector, state => state?.deduplicationRequest);
const getDeduplicationMaterialsSelector = createSelector(getFullFeatureStateSelector, state => state?.deduplicationRequest.materialsDeduplication);
const getLicenseConfigurationSelector = createSelector(getFullFeatureStateSelector, state => state?.licenseConfigurations);
const getEnrichmentMaterialDetailsSelector = createSelector(getFullFeatureStateSelector, state => state?.enrichmentMaterialDetails);
const getAvailableStatusesByClientSelector = createSelector(getFullFeatureStateSelector, state => state?.availableStatusesByClient);
const getGroupClientsSelector =  createSelector(getFullFeatureStateSelector, state => state?.groupClients);
const getCurrentClientSelector =  createSelector(getFullFeatureStateSelector, state => state?.currentClient);

export const DeduplicationSelectors = {
    getFullFeatureState: getFullFeatureStateSelector,
    getIsLoading: isLoadingSelector,
    getHasError: hasErrorSelector,
    getStep: getStepSelector,
    getStepNumber: getStepNumberSelector,
    getStepper: getStepperSelector,
    getRelationshipType: getRelationshipTypeSelector,
    getMaterialIds: getMaterialIdsSelector,
    getMaterials: getMaterialsSelector,
    getRelationshipValidateDraft: getRelationshipValidateDraftSelector,
    getIsCreatingGoldenRecord: getIsCreatingGoldenRecordSelector,
    getClientErrors: getClientErrorsSelector,
    getCrossClient: getCrossClientSelector,
    getSubtypesRelationships: getSubtypesRelationshipsSelector,
    getSelectedSubtypesRelationships: getSelectedSubtypesRelationshipsSelector,
    getDeduplicationRequest: getDeduplicationRequestSelector,
    getLicenseConfiguration: getLicenseConfigurationSelector,
    getEnrichmentMaterialDetails: getEnrichmentMaterialDetailsSelector,
    getAvailableStatusesByClient: getAvailableStatusesByClientSelector,
    getDeduplicationMaterials: getDeduplicationMaterialsSelector,
    getGroupClients: getGroupClientsSelector,
    getCurrentClient: getCurrentClientSelector


};
