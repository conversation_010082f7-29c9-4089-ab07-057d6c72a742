import { ObjectsUtils } from 'src/app/utils';
import { RelationshipType } from '../../relationships/endpoint/relationships.model';
import {
  DEDUPLICATION_ACTION_NAMES,
  DeduplicationAction_GetMasterDataStatus,
  DeduplicationAction_GetMasterDataStatus_Success, DeduplicationAction_Init_Stepper, DeduplicationAction_Init_Stepper_Success,
  DeduplicationAction_GetEnrichmentMaterialDetails_Success,
  DeduplicationAction_RelationshipCreationTogglePrimary,
  DeduplicationAction_RelationshipCreationToggleSelected,
  DeduplicationAction_RelationshipTypeSelection,
  DeduplicationAction_SelectedSubtypesRelationships,
  DeduplicationAction_SetCreatingGoldenRecord,
  DeduplicationAction_SetMaterialIds,
  DeduplicationAction_SetRelationshipValidateError,
  DeduplicationAction_SetRequestNote,
  DeduplicationAction_SetSlaveStatusInARelationship,
  DeduplicationAction_SetStepPage,
  DeduplicationAction_SubtypesRelationships,
  DeduplicationAction_UpdateCurrentClient,
  DeduplicationAction_ValidateDeduplication_Failure,
  DeduplicationAction_ValidateDeduplicationStep,
  DeduplicationAction_ValidateDeduplicationStep_Failure,
  DeduplicationAction_ValidateDeduplicationStep_Success,
  DeduplicationAction_UpdateEnrichmentFieldSelection,
  DeduplicationAction_BulkSelectEnrichmentColumn,
  DeduplicationActionTypes
} from './deduplication.actions';
import { DeduplicationState, TAM_DEDUPLICATION_INITIAL_STATE } from './deduplication.state';
import {applySecondarySelection} from '../common/deduplication-utils';
import {
    DeduplicationBasicDataRowDto,
    DeduplicationEnrichedDataDetails,
    DeduplicationFieldConfiguration
} from '../models/deduplication.models';

export class DeduplicationReducer {
    public static reduce(state: DeduplicationState = TAM_DEDUPLICATION_INITIAL_STATE,
                         action: DeduplicationActionTypes = {type: null}): DeduplicationState {
        switch (action.type) {
            case DEDUPLICATION_ACTION_NAMES.INIT:
                return DeduplicationReducer.doInit(state, action);
            case DEDUPLICATION_ACTION_NAMES.INIT_STEPPER:
                return DeduplicationReducer.doInitStepper(state, action);
            case DEDUPLICATION_ACTION_NAMES.INIT_STEPPER_SUCCESS:
                return DeduplicationReducer.doInitStepperSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_MATERIAL_IDS:
                return DeduplicationReducer.doSetMaterialIds(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_TYPE_SELECTION:
                return DeduplicationReducer.doRelationshipTypeSelection(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_SLAVE_STATUS_IN_A_RELATIONSHIP:
                 return DeduplicationReducer.doSetSlaveStatusInARelationship(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_STEP_PAGE:
                 return DeduplicationReducer.doSetStepPage(state, action);
           case DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS:
                 return DeduplicationReducer.doGetRelationshipMaterialsDetails(state, action);
           case DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS_SUCCESS:
                 return DeduplicationReducer.doGetRelationshipMaterialsDetailsSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS_FAILURE:
                 return DeduplicationReducer.doDefaultFailure(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_SELECTED:
                 return DeduplicationReducer.doToggleSelected(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_PRIMARY:
                 return DeduplicationReducer.doTogglePrimary(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_CREATING_GOLDEN_RECORD:
                 return DeduplicationReducer.doSetCreatingGoldenRecord(state, action);
           case DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS:
                 return DeduplicationReducer.doGetSubtypesRelationship(state, action);
           case DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS_SUCCESS:
                 return DeduplicationReducer.doGetSubtypesRelationshipSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS_FAILURE:
                 return DeduplicationReducer.doGetSubtypesRelationshipFailure(state, action);
           case DEDUPLICATION_ACTION_NAMES.SELECTED_SUBTYPES_RELATIONSHIPS:
                 return DeduplicationReducer.doSelectedSubtypesRelationships(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_RELATIONSHIP_VALIDATE_ERROR:
               return DeduplicationReducer.doSetRelationshipValidateError(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_IS_VALID:
               return DeduplicationReducer.doRelationshipIsValid(state, action);
           case DEDUPLICATION_ACTION_NAMES.CLEAR_RELATIONSHIP_VALIDATE_ERROR:
               return DeduplicationReducer.doClearRelationshipValidateError(state, action);
           case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_STEP:
               return DeduplicationReducer.doValidateDeduplicationStep(state, action);
           case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_STEP_SUCCESS:
               return DeduplicationReducer.doValidateDeduplicationStepSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_STEP_FAILURE:
               return DeduplicationReducer.doValidateDeduplicationStepFailure(state, action);
           case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_FAILURE:
               return DeduplicationReducer.doValidateDeduplicationFailure(state, action);
            case DEDUPLICATION_ACTION_NAMES.INIT_LICENSE_CONFIGURATION:
                return DeduplicationReducer.doInitLicenseConfiguration(state, action);
            case DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS:
                return DeduplicationReducer.doGetEnrichmentMaterialDetails(state, action);
            case DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS_SUCCESS:
                return DeduplicationReducer.doGetEnrichmentMaterialDetailsSuccess(state, action);
            case DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS:
                 return DeduplicationReducer.doGetMasterDataStatus(state, action);
            case DEDUPLICATION_ACTION_NAMES.UPDATE_CURRENT_CLIENT:
                 return DeduplicationReducer.doUpdateCurrentClient(state, action);
           case DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS_SUCCESS:
                 return DeduplicationReducer.doGetMasterDataStatusSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS_FAILURE:
                 return DeduplicationReducer.doDefaultFailure(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_REQUEST_NOTE:
                 return DeduplicationReducer.doSetRequestNote(state, action);
           case DEDUPLICATION_ACTION_NAMES.UPDATE_ENRICHMENT_FIELD_SELECTION:
                 return DeduplicationReducer.doUpdateEnrichmentFieldSelection(state, action);
           case DEDUPLICATION_ACTION_NAMES.BULK_SELECT_ENRICHMENT_COLUMN:
                 return DeduplicationReducer.doBulkSelectEnrichmentColumn(state, action);
           default:
              return state;
       }
   }

   public static doInit(state: DeduplicationState, action: DeduplicationActionTypes) {
      return TAM_DEDUPLICATION_INITIAL_STATE;
   }

    public static doInitStepper(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_Init_Stepper = ObjectsUtils.forceCast<DeduplicationAction_Init_Stepper>(action);
        newState.loading = true;
        return newState;
    }


    public static doInitStepperSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_Init_Stepper_Success = ObjectsUtils.forceCast<DeduplicationAction_Init_Stepper_Success>(action);
        newState.stepper = a.payload;
        newState.step = newState.step ?? newState.stepper[0];
        newState.loading = false;
        return newState;
    }

   public static doSetMaterialIds(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetMaterialIds = ObjectsUtils.forceCast<DeduplicationAction_SetMaterialIds>(action);
      newState.materialIds = a.payload;
      newState.deduplicationRequest = {
            ...newState.deduplicationRequest,
            materialsInRelationship: a.payload.map(materialId => ({
                materialId,
                role: null,
                primary: false,
                selected: false,
                status: null,
                materialStatus: null
            }))
      };
      return newState;
   }

   public static doRelationshipTypeSelection(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_RelationshipTypeSelection = ObjectsUtils.forceCast<DeduplicationAction_RelationshipTypeSelection>(action);
      const subtypesRelationships = newState.subtypesRelationships
          ?.filter(el => el.relationshipType.toUpperCase() === a.payload)
          .find(el => el.subtypes.length === 1);

      newState.deduplicationRequest.relationshipType = RelationshipType[a.payload];
      newState.deduplicationRequest.subtype = subtypesRelationships ? subtypesRelationships.subtypes[0] : null;
      return newState;
   }

   public static doSetSlaveStatusInARelationship(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetSlaveStatusInARelationship = ObjectsUtils.forceCast<DeduplicationAction_SetSlaveStatusInARelationship>(action);

      newState.groupMaterials.materials = newState.groupMaterials.materials?.map(
          m => ({
              ...m,
              status: m.materialId === a.payload.material.materialId ? a.payload.status : m.status,
          })
      );

      if (a.payload.status !== null && typeof a.payload.status === 'object') {
          a.payload.status = "";
      }

      newState.deduplicationRequest.materialsDeduplication = newState.deduplicationRequest.materialsDeduplication?.map(
          m => ({
              ...m,
              status: m.materialId === a.payload.material.materialId ? a.payload.status : m.status,
          })
      );

      return newState;
   }

   public static doSetStepPage(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetStepPage = ObjectsUtils.forceCast<DeduplicationAction_SetStepPage>(action);
      newState.step = newState.stepper[a.payload];
      return newState;
   }

   public static doGetRelationshipMaterialsDetails(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);

      newState.isLoading = true;
      return newState;
   }

   public static doGetRelationshipMaterialsDetailsSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetSlaveStatusInARelationship = ObjectsUtils.forceCast<DeduplicationAction_SetSlaveStatusInARelationship>(action);

      newState.isLoading = false;
      newState.groupMaterials.materials = a.payload.materials.map((value) => ({
         ...value,
         selected: false
      }));
      return newState;
   }

   public static doDefaultFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      newState.isLoading = false;
      return newState;
   }

   public static doGetSubtypesRelationship(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SubtypesRelationships = ObjectsUtils.forceCast<DeduplicationAction_SubtypesRelationships>(action);
      newState.subtypesRelationships = a.payload;
      newState.loading = true;
      return newState;
   }

  public static doGetSubtypesRelationshipSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
    const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
    const a: DeduplicationAction_SubtypesRelationships = ObjectsUtils.forceCast<DeduplicationAction_SubtypesRelationships>(action);

    newState.deduplicationRequest.subtype = a.payload && a.payload[0]?.subtypes.length === 1 ? a.payload[0].subtypes[0] : null;
    newState.subtypesRelationships = a.payload;
    newState.loading = true;
    return newState;
  }

  public static doGetSubtypesRelationshipFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
    const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
    newState.loading = false;
    newState.subtypesRelationships = null;
    return newState;
  }

  public static doSelectedSubtypesRelationships(state: DeduplicationState, action: DeduplicationActionTypes) {
    const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
    const a: DeduplicationAction_SelectedSubtypesRelationships = ObjectsUtils.forceCast<DeduplicationAction_SelectedSubtypesRelationships>(action);

    newState.deduplicationRequest.subtype = a.payload;
    newState.loading = false;
    return newState;
  }

  public static doSetCreatingGoldenRecord(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetCreatingGoldenRecord = ObjectsUtils.forceCast<DeduplicationAction_SetCreatingGoldenRecord>(action);

      newState.deduplicationRequest.isCreatingGoldenRecord = a.payload;
      return newState;
  }

  public static doTogglePrimary(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_RelationshipCreationTogglePrimary = ObjectsUtils.forceCast<DeduplicationAction_RelationshipCreationTogglePrimary>(action);

      const affectedClient =
        newState.groupMaterials.materials.find(
          (m) => m.materialId === action.payload
        )?.client;
      newState.groupMaterials.materials = newState.groupMaterials.materials.map(
              (m) => ({
                ...m,
                primary:
                  m.materialId === a.payload
                    ? !m.primary
                    : m.client === affectedClient
                    ? false
                    : newState.crossClient
                    ? false
                    : m.primary,
                selected:
                  m.materialId === a.payload
                    ? true
                    : m.selected,
              })
            );
      newState.isSubmittingData = true;

      const selectedMaterials = newState.groupMaterials.materials.filter(el => el.selected);
      newState.deduplicationRequest.materialsDeduplication = selectedMaterials;
      newState.groupClients = [...new Set(selectedMaterials.map(el => el.client))];
      return newState;
  }

  public static doToggleSelected(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_RelationshipCreationToggleSelected = ObjectsUtils.forceCast<DeduplicationAction_RelationshipCreationToggleSelected>(action);

      newState.groupMaterials.materials = newState.groupMaterials.materials.map(
              (m) => ({
                ...m,
                selected:
                  m.materialId === a.payload
                    ? !m.selected
                    : m.selected,
                primary:
                  m.materialId === a.payload && m.selected
                    ? false
                    : m.primary,
              })
            );
      newState.isSubmittingData = true;

      const selectedMaterials = newState.groupMaterials.materials.filter(el => el.selected);
      newState.deduplicationRequest.materialsDeduplication = selectedMaterials;
      newState.groupClients = [...new Set(selectedMaterials.map(el => el.client))];
      return newState;
  }

   public static doSetRelationshipValidateError(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetRelationshipValidateError = ObjectsUtils.forceCast<DeduplicationAction_SetRelationshipValidateError>(action);

      newState.relationshipValidateDrafts = a.payload.relationshipValidateDrafts;
      newState.clientErrors = a.payload.clientValidation;
      newState.crossClient = action.payload.crossClient;
      newState.isValid = false;
      newState.errorRelationshipMessage = null;
      newState.isSubmittingData = false;
      newState.step = {
            ...newState.step,
            isValid: false
      };
      return newState;
   }

   public static doRelationshipIsValid(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetRelationshipValidateError = ObjectsUtils.forceCast<DeduplicationAction_SetRelationshipValidateError>(action);

      newState.errorRelationshipMessage = null;
      newState.relationshipValidateDrafts = [];
      newState.clientErrors = null;
      newState.isSubmittingData = false;
      newState.isValid = true;
      newState.crossClient = a.payload.crossClient;
      newState.step = {
         ...newState.step,
         isValid: true
      };
      return newState;
   }

   public static doClearRelationshipValidateError(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);

      newState.relationshipValidateDrafts = [];
      newState.clientErrors = null;
      newState.isValid = false;
      newState.errorRelationshipMessage = null;
      newState.isSubmittingData = false;
      newState.step = {
         ...newState.step,
         isValid: false
      };
      return newState;
   }

    private static doValidateDeduplicationStep(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplicationStep = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplicationStep>(action);

        return newState;
    }

    private static doValidateDeduplicationStepSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplicationStep_Success = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplicationStep_Success>(action);

        newState.isValid = true;
        newState.step = {
            ...newState.step,
            isValid: true
        };
        return newState;
    }

    private static doValidateDeduplicationStepFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplicationStep_Failure = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplicationStep_Failure>(action);

        newState.isValid = false;
        newState.step = {
            ...newState.step,
            isValid: false
        };
        return newState;
    }

    private static doValidateDeduplicationFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplication_Failure = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplication_Failure>(action);

        newState.isValid = false;
        newState.step = {
            ...newState.step,
            isValid: false
        };
        return newState;
    }

    private static doInitLicenseConfiguration(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplication_Failure = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplication_Failure>(action);

        newState.licenseConfigurations = a.payload;
        return newState;
    }

    private static doGetEnrichmentMaterialDetails(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        newState.loading = true;
        newState.currentClient = newState.groupClients?.length > 0 ? newState.groupClients[0] : '100';
        return newState;
    }

    private static doGetEnrichmentMaterialDetailsSuccess(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_GetEnrichmentMaterialDetails_Success =
            ObjectsUtils.forceCast<DeduplicationAction_GetEnrichmentMaterialDetails_Success>(action);
        newState.enrichmentMaterialDetails = a.payload;
        const enrichedData: { [rowId: string]: { [fieldKey: string]: DeduplicationFieldConfiguration } } = {};
        a.payload.forEach((group: DeduplicationEnrichedDataDetails) => {
            group.rows.forEach((row: DeduplicationBasicDataRowDto) => {
                enrichedData[row.id] = {};
                Object.entries(row.primary || {}).forEach(([key, value]) => {
                    enrichedData[row.id][key] = { ...value };
                });
            });
        });

        newState.enrichedMaterialsData = {
            [newState.currentClient]: enrichedData
        };

        newState.loading = false;
        return newState;
    }

    public static doGetMasterDataStatus(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_GetMasterDataStatus = ObjectsUtils.forceCast<DeduplicationAction_GetMasterDataStatus>(action);
      newState.loading = true;
      return newState;
    }

    public static doUpdateCurrentClient(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_UpdateCurrentClient = ObjectsUtils.forceCast<DeduplicationAction_UpdateCurrentClient>(action);
      newState.currentClient = a.payload;
      return newState;
    }

    public static doGetMasterDataStatusSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_GetMasterDataStatus_Success = ObjectsUtils.forceCast<DeduplicationAction_GetMasterDataStatus_Success>(action);

      newState.deduplicationRequest.materialsDeduplication = newState.deduplicationRequest.materialsDeduplication?.map(m => {
        const clientData = a.payload?.[m.client];

        if (clientData) {
          if (clientData.length === 1) {
            return {
              ...m,
              status: clientData[0].key
            };
          }

          if (clientData.some(s => s.key === m.status)) {
            return m;
          }
        }

        return {
          ...m,
          status: ""
        };
      });

      newState.availableStatusesByClient = a.payload;
      newState.loading = true;
      return newState;
    }

    public static doSetRequestNote(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetRequestNote = ObjectsUtils.forceCast<DeduplicationAction_SetRequestNote>(action);

      newState.deduplicationRequest.note = a.payload;
      return newState;
    }
    public static doUpdateEnrichmentFieldSelection(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_UpdateEnrichmentFieldSelection>(action);

        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                if (row.id === a.payload.fieldConfiguration.id) {
                    const secondaryField = row.secondaries[a.payload.materialKey];
                    applySecondarySelection(row, secondaryField, a.payload.materialKey, true, newState.enrichedMaterialsData[newState.currentClient]);
                }
            });
        });

        return newState;
    }

    public static doBulkSelectEnrichmentColumn(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_BulkSelectEnrichmentColumn>(action);
        const columnMaterialId = a.payload.columnMaterialId;
        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                const secondaryField = row.secondaries[columnMaterialId];
                applySecondarySelection(row, secondaryField, columnMaterialId, true, newState.enrichedMaterialsData[newState.currentClient]);
            });
        });
        return newState;
    }


}
