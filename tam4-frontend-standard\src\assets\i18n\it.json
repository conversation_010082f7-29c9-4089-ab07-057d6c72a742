{"audit": {"actions": {"all": "<PERSON><PERSON>", "material_edit_workflow_started": "Modifica materiale: workflow avviato", "material_edit_patch_assigned": "Modifica materiale: patch assegnata", "material_edit_patch_created": "Modifica materiale: patch creata", "material_edit_approved": "Modifica materiale: patch approvata", "material_edit_rejected": "Modifica materiale: patch rifiutata", "material_edit_integrated": "Modifica materiale: integrata nell'ERP", "material_edit_completed": "Modifica materiale: patch completata", "material_edit_patch_failed": "Modifica materiale: patch fallita", "material_edit_workflow_ended": "Modifica materiale: workflow terminato", "material_creation_workflow_started": "Modifica materiale: workflow avviato", "material_creation_approved": "Creazione materiale: approvata", "material_creation_assigned": "Creazione materiale: in attesa di patch", "material_creation_rejected": "Creazione materiale: rifiutata", "material_creation_integrated": "Creazione materiale: integrata in ERP", "material_creation_completed": "Creazione materiale: completata", "material_creation_failed": "Creazione materiale: fallita", "material_creation_workflow_ended": "Creazione materiale: workflow terminato", "material_extension_edit_workflow_started": "Modifica estensione materiale: workflow avviato", "material_extension_integrated": "Modifica estensione materiale: integrata in ERP", "material_extension_edit_workflow_ended": "Modifica estensione materiale: workflow terminato", "material_extension_creation_workflow_started": "Creazione estensione materiale: workflow avviato", "material_extension_creation_workflow_ended": "Creazione estensione materiale: workflow terminato", "material_deduplicate_workflow_started": "Deduplica materiale: workflow avviato", "material_deduplicate_workflow_ended": "Deduplica materiale: workflow terminato", "material_relationship_workflow_started": "Relazione materiale: workflow avviato", "relationship_material_erp_updated": "Relazione materiale: integrata in ERP", "material_relationship_failed": "Relazione materiale: fallita", "material_edit_approveds_relationship": "Relazione materiale: workflow approvato", "material_edit_rejecteds_relationship": "Relazione materiale: workflow rifiutato", "material_relationship_workflow_ended": "Relazione materiale: workflow terminato", "user_requested_material_creation": "Richiesta creazione materiale", "user_requested_material_extension": "Richiesta estensione materiale", "material_edit_approved_extension": "Estensione materiale approvata", "material_edit_approved_extension_edit": "Modifica estensione materiale approvata", "material_edit_rejected_extension": "Estensione materiale rifiutata", "material_edit_rejected_extension_edit": "Modifica estensione materiale rifiutata", "material_extension_edit_created": "Modifica estensione materiale creata", "user_requested_material_extension_edit": "Richiesta modifica estensione materiale", "material_rejected": "Materiale rifiutato", "material_warning_created": "Avviso materiale creato", "material_warning_deleted": "Avviso materiale cancellato", "user_requested_materials_deduplicate": "Richiesta deduplica materiale", "materials_deduplicate_completed": "Deduplica materiale completata", "material_created": "Materiale creato", "relationship_rejected": "Relazione rifiutata", "relationship_created": "Relazione creata", "material_updated": "Materiale aggiornato", "material_relationship_updated": "Relazione aggiornata", "material_relationship_deleted": "Relazione eliminata", "material_warning_managed": "Avviso elaborato", "material_extension_edit": "Estensione aggiornata", "material_extension_created": "Estensione creata", "materials_deduplicated": "Materiale deduplicato", "material_creation_imported": "Creazione materiale importata", "material_extension_imported": "Estensione materiale importata", "material_golden_record_deleted": "Il golden record è stato cancellato", "role_update": "<PERSON><PERSON><PERSON>", "role_creation": "<PERSON><PERSON><PERSON>", "role_delete": "<PERSON><PERSON><PERSON>", "export_role_excel": "Export Excel dei Ruoli", "group_creation": "Gruppo Creato", "group_update": "Gruppo Aggiornato", "group_delete": "Gruppo Cancellato", "export_group_excel": "Export Excel dei Gruppi", "custom_role_creation": "Custom Role Creato", "custom_role_update": "Custom Role Aggiornato", "custom_role_delete": "Custom Role Cancellato", "user_role_add": "Ruoli Utente Aggiunti", "user_role_update": "Ruoli Utente Aggiornati", "user_role_set": "Ruoli Utente Settati", "user_role_delete": "Ruoli Utente Cancellati", "user_block_account": "Utente Bloccato", "user_unblock_account": "Utente Sbloccato", "user_resend_activation_mail": "Rinviata mail di attivazione", "user_create": "Utente Creato", "user_update": "Utente Aggiornato", "delete_users": "Utente Cancellato", "change_mail_settings": "Impostazioni Mail Aggiornate", "change_fallback_languages": "Fallback Languages Aggiornate", "crate_user_with_roles": "Creazione Utente con Ruoli", "export_users_excel": "Export Excel degli Utenti"}, "audit-view": {"paging-title": "Visualizzati: {{itemsInPage}} risultati su {{resultCount}}", "filters": {"date-range": "Intervallo di date", "action-type": "Tipo di azione", "user": "Utente", "material": "Materiale"}, "testing": "Test traduzione in corso", "additional": {"assigned-to-user": "Assegnato a utente", "assigned-to-roles": "<PERSON><PERSON><PERSON><PERSON> al ruolo", "warning-type": "Tipi di avviso", "system-admin": "Amministratore di sistema"}}, "loading-view": {"searching": "Ricerca..."}, "no-result-view": {"title": "<PERSON><PERSON><PERSON> r<PERSON>", "subheading": "<PERSON><PERSON><PERSON> risultato nel registro revisioni", "suggestion-title": "Suggerimenti:", "line1": "Assicurati di possedere i giusti permessi", "line2": "Prova una combinazione di filtri diversa"}, "error-view": {"head-text": "Si è verificato un errore durante l'elaborazione della richiesta"}, "errors": {"default": "Qualcosa è andato storto, riprova più tardi", "save": "Un errore si è verificato durante il salvataggio, si prega di verificare i dati e riprovare.", "materials": {"material-with-this-id-not-found": "Materiale non trovato!"}}}, "bulk-upload": {"table": {"REQUESTED": "Creazione richiesta", "STARTED": "Processo iniziato", "WAITING_FOR_EDIT": "In attesa di modifica", "WAITING_FOR_APPROVAL": "In attesa di approvazione", "WAITING_FOR_ERP": "In attesa di ERP", "COMPLETED": "Creazione completata", "REJECTED": "Creazione rifiutata", "DUPLICATE": "Riga contrassegnata come duplicato", "DELETED": "Riga rimossa", "ABORTED": "Creazione interrotta"}, "fields": {"material-type": "Tipo di materiale", "material-group": "Gruppo materiale", "base-unit-of-measure": "Unità di misura di base", "manufacturer-part-number": "Codice Materiale Produttore", "short-text": "Testo breve", "long-text-md": "<PERSON>o lungo", "long-text-po": "<PERSON>o lungo P<PERSON>", "purchasing-group": "Gruppo di acquisto"}, "pagination": {"first": "Primo", "previous": "Precedente", "next": "Successivo", "last": "Ultimo"}, "process-status": {"uploaded": "Caricato", "syntactic-validation-in-progress": "Valutazione sintattica in corso", "syntactic-validation": "Valutazione sintattica da rivedere", "semantic-classification": "Classificazione semantica da rivedere", "waiting-for-completion": "In attesa di completamento", "completed": "Completato", "duplicates-check": "Controllo dei duplicati", "archived": "Archiviato", "deleted": "Cancellato", "editing": "Modifica", "data-ingestion": "Data ingestion", "ai-analysis": "ai-analysis", "file-validation": "Validazione file", "file-validation-errors": "Errori di validazione del file", "invalid-file": "File non valido", "duplicates-request-to-be-done": "Richieste su Duplicati da completare", "duplicates-waiting-for-report": "<PERSON><PERSON><PERSON><PERSON> in attesa di report", "duplicates-report-to-be-retreived": "Report duplicati da recuperare", "duplicates-to-be-reviewed": "Duplicati da rivedere", "sending-creation-requests": "Invia richieste di creazione", "waiting-for-process-completion": "In attesa di completamento del processo", "finished": "Concluso", "terminated": "Cancellato", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "file-validation-errors-internal": "Errori di validazione del file (interno)", "ignore": "Ignora", "data-default-enrichment": "Arricchimento predefinito dei dati", "file-validation-internal": "Validazione file (interno)"}, "errors": {"error-view": {"head-text": "Errore durante l'elaborazione della richiesta", "all-errors": "<PERSON><PERSON> gli errori ({{value}})"}, "default": "Qualcosa è andato storto, per favore riprova più tardi", "bom-critical-errors": "Errore critico durante la validazione della struttura della BOM", "critical-errors": "Report errori bloc<PERSON>ti", "validation-generic-error": "Errore durante la validazione del file", "invalidFileUploaded": "Il file caricato non è valido", "cannot-delete-bom-present": "Non posso cancellare, è presente una BOM", "cannot-generate-excel-template": "Non posso generare il template Excel", "file-stream-error": "Errore di stream del file", "master-data-does-not-belong-to-process": "Masterdata non appartiene al processo", "masterdata-not-updated": "Masterdata non aggiornato", "process-not-found": "Processo non trovato", "reclassification-error": "Errore durante la riclassificazione dei dettagli del Master Data", "status-not-found": "Stato non trovato", "status-not-recognized": "Stato non riconosciuto", "unauthorized-action": "Non sei autorizzato ad eseguire l'azione", "user-type-not-found": "Tipo utente non trovato"}, "operations": {"completed": "L'operazione è stata completata con successo", "processing-data": "Elaborazione dei dati"}, "search": {"title": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "subheading": "Non ci sono risultati corrispondenti ai tuoi filtri", "suggestion-title": "Suggerimenti:", "line1": "Prova una combinazione di filtri differente", "line2": "Prova a resettare i tuoi filtri"}, "bulk-card-upload": {"file-upload": "Carica file", "download-file-materials": "Ottieni file (materiali)", "download-file-services": "Ottieni file (servizi)", "custom-template-upload": "Carica template personalizzato", "custom-template-download": "Scarica template"}, "syntactic-validation": {"popup": {"header": {"attribute": "Attributo", "value": "Valore", "error": "Errore"}}, "navigation-header": {"blocking-errors": "<PERSON><PERSON><PERSON>", "warnings": "Avvisi", "materials-with-issues": "Materiali con problemi", "materials-without-issues": "Anagrafiche corrette", "errors-report": "Anagrafiche con errori"}, "errors": {"title": "Nessun materiale è stato trovato per il tipo di errore selezionato", "filter-materials": "Seleziona un tipo di errore:", "material-group": {"empty": "Gruppo materiale non è definito", "not-in-list": "Gruppo materiale non è riconosciuto"}, "material-type": {"empty": "Tipo di materiale non è definito", "not-in-list": "Tipo di materiale non in elenco"}, "base-unit-of-measure": {"empty": "Unità di misura di base non è definito", "not-in-list": "Unità di misura non è riconosciuto"}, "manufacturer-part-number": {"empty": "Codice Materiale Produttore è vuoto"}, "short-text": {"empty": "Testo breve è vuoto", "limit": "Raggiunta la lunghezza massima per i campi di testo breve"}, "long-text-md": {"empty": "Testo lungo è vuoto"}, "long-text-po": {"empty": "Testo lungo PO è vuoto"}, "purchasing-group": {"empty": "Il gruppo d'acquisto è vuoto", "not-in-list": "Il gruppo d'acquisto non è riconosciuto"}, "bom": {"bom-row-id-equal-to-parent": "BOM Row ID è uguale a BOM Parent ID", "bom-row-id-must-not-be-empty": "La riga relativa alla BOM non può essere vuota", "bom-loop": "All'interno della riga Parent Row ID non deve essere uguale a Row ID ", "bom-quantity-empty": "Quantità della BOM deve essere maggiore di 0", "bom-multi-client": "Rilevati valori multipli del client", "parent-not-found": "Parent ID not found"}, "client": {"empty": "Il Mandante è vuoto", "not-in-list": "Client non nella lista", "not-authorized": "<PERSON><PERSON><PERSON> permesso per il client"}, "mdDomain": {"empty": "Campo di dominio non può essere vuoto"}, "plant": {"not-valid-value": "Plant - valore non permesso", "not-authorized": "<PERSON><PERSON><PERSON> permesso per il plant"}, "short-text-md": {"empty": "Il campo Short text è vuoto", "limit": "Raggiunta la lunghezza massima per il campo Short Text", "duplicates": {"it": "Riga duplicata per Short Text ITALIANO", "en": "Riga duplicata per Short Text INGLESE", "es": "Riga duplicata per Short Text SPAGNOLO", "de": "Riga duplicata per Short Text TEDESCO", "pt": "Riga duplicata per Short Text PORTOGHESE", "fr": "Riga duplicata per Short Text FRANCESE", "ru": "Riga duplicata per Short Text RUSSO", "zh": "Riga duplicata per Short Text CINESE", "ko": "Riga duplicata per Short Text COREANO", "ro": "Riga duplicata per Short Text RUMENO", "gr": "Riga duplicata per Short Text GRECO", "pl": "Riga duplicata per Short Text POLACCO", "cs": "Riga duplicata per Short Text CECO", "hr": "Riga duplicata per Short Text croata", "iw": "Riga duplicata per Short Text EBRAICO"}}, "manufacturer-code-and-manufacturer-part-number": {"duplicates": "Riga duplicata per 'Produttore' e 'Codice Materiale Produttore'"}, "unknown-error-while-parsing": "Si è verificato un errore sconosciuto durante l'analisi del file Excel", "unknown-error-while-validating": "Si è verificato un errore sconosciuto durante la convalida del file Excel", "unhandled-column": "Colonna non gestita in Excel: {{param}}", "warehouse": {"not-valid-value": "Magazzino - valore non ammesso"}, "category": {"empty": "Categoria non selezionata"}, "descriptions": {"empty": "<PERSON>tte le descrizioni sono vuote, inserirne almeno una"}, "material-key-masterdata-exist": "Il codice materiale caricato è già esistente nel database per il cliente specifico"}, "warnings": {"title": "Nessuna anagrafica trovato per il tipo di avviso selezionato", "filter-materials": "Seleziona un tipo di avviso:"}, "material-errors-popup": {"row-number": "Dettagli di riga #"}, "materials-with-issues": {"no-materials-found": "Non sono state trovate anagrafiche con problemi sintattici"}, "materials-without-issues": {"no-materials-found": "Non sono state trovate anagrafiche senza problemi"}, "no-data-available": "<PERSON><PERSON><PERSON> dato disponibile", "continue": "Continua", "edit-selected": "Modifica selezionata", "edit-errors-modal": {"edit-all": "Modifica errori per riga #", "title": "Modifica valori per campo", "selected-number": "Numero di articoli selezionati:", "row-number": "Numero di riga:", "error-type": "<PERSON><PERSON><PERSON> di errore:", "field-name": "Nome del campo:", "current-value": "<PERSON><PERSON> corrente:", "new-value": "Nuovo valore", "insert-value": "Inserisci valore...", "no-data": "<PERSON><PERSON><PERSON> dato disponibile", "cancel": "Can<PERSON><PERSON>", "save": "<PERSON><PERSON>", "search-value": "Cerca per un valore ..."}, "delete-materials": {"delete-selected": "Elimina selezionato", "delete-materials-successful": "Le anagrafiche selezionate sono stati cancellate con successo!"}, "affected-rows": "<PERSON><PERSON><PERSON> impattate", "critical-error-title": "PROCESSO TERMINATO DA ERRORI BLOCCANTI (Gli errori devono essere risolti manualmente nel file sorgente)", "confirm-popup": {"title": "Categorizzazione del Master data da confermare", "message": "Alcune righe che devono ancora essere validate con livello di confidenza Medio o Buono. Procedere ugualmente?", "actions": {"cancel": "Can<PERSON><PERSON>", "confirm": "Continua"}}, "waiting-for-internal-user": {"title": "The current process is WAITING", "message": "Waiting for internal user operations..."}, "error-during-validation": "Errore sconosciuto durante la validazione del file"}, "semantic-classification": {"confirm-selected": "Conferma la selezione", "navigation-header": {"confirmed-materials": "Materiali confermati", "poor-completeness": "<PERSON>arsa <PERSON>", "fair-completeness": "Completezza discreta", "good-completeness": "<PERSON><PERSON><PERSON><PERSON>"}, "empty-state": {"title": "Nessun anagrafica trovata per il livello di affidabilità scelto", "subtitle": "Le anagrafiche generate per l'affidabilità o stato selezionati appariranno qui"}, "change-category-modal": {"title": "Cambia classificazione", "row-number": "Numero di riga :", "description": "Descrizione", "suggestested": {"material-group-classification": "Classificazioni suggerite per gruppi di materiali:", "enriched-material-group": "Gruppi di materiali suggeriti arricchiti:", "technical-classification": "Classificazioni tecniche suggerite:"}, "no-data": "<PERSON><PERSON><PERSON> dato disponibile", "manual-search": "<PERSON><PERSON><PERSON> di questi è corretto? Prova a cercare manualmente qui", "cancel": "Can<PERSON><PERSON>", "confirm-selection": "Conferma selezione", "change-to": "Cambia in"}, "continue": "Continua"}, "duplicates-check": {"navigation-header": {"low": "<PERSON><PERSON>", "medium": "Medio", "high": "Alto", "none": "<PERSON><PERSON><PERSON>", "to-create": "Da creare"}, "modal": {"group-card": {"basic-data": "Dati di base", "description": "Descrizione", "unit-of-measure": "Unità di misura", "source": "Fonte", "material-type": "Tipo di materiale", "manufacturer-part-number": "Codice Materiale Produttore", "long-text-md": "<PERSON><PERSON> lungo <PERSON>", "long-text-po": "<PERSON>o lungo P<PERSON>"}, "select-for-use": "Seleziona per l'uso", "duplicate-detected": "Potenziale duplicato rilevato", "select": "Seleziona", "ignore": "Ignora"}, "table": {"row-number": "Numero riga", "description": "Descrizione", "action": "Azione", "edit": "Modifica"}}, "creation-requests": {"navigation-header": {"creations-requested": "Creazioni richieste", "creations-completed": "Creazioni completate", "creations-rejected": "Creazioni rifiutate", "rows-ignored": "<PERSON><PERSON><PERSON> ignorate"}, "empty-state": {"title": "Nessun materiale trovato per lo stato di workflow scelto", "subtitle": "I materiali generati per lo stato del workflow selezionato appariranno qui"}}, "terminate-process": {"terminate-button": "Termina", "title": "Termina processo", "confirm-message": "Vuoi procedere ?", "cancel": "Can<PERSON><PERSON>"}, "generate-report": "Genera report", "historic-uploads": {"file-name": "Nome del file", "uploaded-by": "Carica<PERSON> da", "upload-date": "Data di caricamento", "see-details": "<PERSON><PERSON><PERSON>", "row-count": "Numero righe", "empty-view": {"title": "Nessun file è stato generato", "subtitle": "I download generati da altri servizi appariranno qui"}, "status": "Stato", "actions": "Azioni", "summary": "<PERSON><PERSON><PERSON>", "upload-id": "ID Caricamento", "upload-start-date": "Intervallo date di caricamento", "bom-masterdata": "BOM/masterdata", "total": "Totale", "upload-description": "Da questa pagina puoi caricare Excel contenenti Master Data.\nSi prega di iniziare scaricando il template Excel in modo da ridurre gli errori in fase di caricamento."}, "file-validation": {"data-quality": {"low": "SCARSO", "medium": "MEDIO", "good": "BUONO", "approved": "VALIDATO", "submitted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "to-rework": "DA RILAVORARE"}, "step": {"uploaded": "Caricato", "file-validation": "Validazione file", "data-validation": "Validazione Dati", "duplicates-check": "Verifica Duplicati", "waiting-for-completition": "In attesa di completamento", "done": "<PERSON><PERSON>"}, "table": {"row-number": "Numero Riga", "description": "Descrizione", "error-types": "Tipi di errore", "action": "Azione", "view-details": "<PERSON><PERSON><PERSON>", "edit": "Modifica", "delete": "Elimina", "field-name": "Nome del campo", "field-value": "Valore del campo"}}, "data-validation": {"no-data-available": "<PERSON><PERSON><PERSON> dato presente in questo tab", "data-quality": "Qualit<PERSON> dati", "categories": "Categorie", "raw-data": "<PERSON><PERSON>", "data-validation": "Validazione Dati", "success-message": "Modifiche correttamente salvate!", "button-labels": {"display-all-categories": "Visualizza Tutte le Categorie", "change-category": "Cambia Categoria", "approve": "Valida", "submit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ignore": "Ignora", "reject": "<PERSON><PERSON><PERSON><PERSON>"}, "table": {"row-number": "Numero Riga", "short-text": "<PERSON>o corto", "uom": "UoM", "manufacturer": "Produttore", "manufacturer-nr": "Nr. del Produttore", "classification": "Classificazione"}, "error-message": "Si è verificato un errore durante la validazione ed il salvataggio!"}, "custom-template": {"all": "<PERSON><PERSON>", "empty": "Standard", "saving-error": "Un errore si è verificato durante il salvataggio", "validator": {"file-type-excel": "Permessi solo file excel con estensione XLSX", "layout-excel": "La disposizione delle colonne è errata", "md-domain-excel": "Il template usa il dominio sbagliato (Materiale/Servizi)", "not-recognized": "I seguenti campi non sono stati riconosciuti:", "expected-field-not-found": "Il template non ha il/i seguenti campi che sono disponibili:"}, "upload-popup": {"template-saved": "Template salvato usando il seguente linguaggio per le intestazioni delle colonne:", "header": "Importazione excel personalizzato per Caricamento Massivo", "clients": "Clients", "clientsTooltip": "Client", "description": "Si prega di caricare un excel personalizzato derivato dallo standard previsto dal caricamento massivo. Questo permette di prevenire errori di validazione quando l'excel personalizzato verrà caricato", "dragNdrop": "Trascina e rilascia il file nella box sottostante, o semplicemente fai click su di esso per lanciare il processo di caricamento", "already-exists-warning": "Esiste già un template per il cliente selezionato. Lo vuoi sostituire?", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "close": "<PERSON><PERSON>"}, "download-popup": {"header": "Scarica excel per Caricamento Massivo", "download": "Scarica", "templates": "Templates", "templatesTooltip": "Template", "languages": "Lingue", "languagesTooltip": "Lingua per le intestazioni"}, "all-custom": "(<PERSON><PERSON><PERSON><PERSON>) <PERSON><PERSON>"}}, "data-loader": {"statistics": {"indicators": {"ignored": "Materiali Ignorati", "analyzed": "<PERSON><PERSON> Analizzati", "total-count": "Materiali Totali"}}, "validation": {"form": {"panel": "Validazione dei file cliente", "title": "Si prega di completare questo form", "files": {"label": "File compresso (*.zip)", "placeholder": "Seleziona un file"}, "email": {"label": "Email", "placeholder": "<PERSON>o indirizzo e-mail aziendale"}, "scope": {"label": "Ambito", "placeholder": "es. caricamento completato, aggiornamento IT, ..."}, "notify": {"label": "<PERSON><PERSON><PERSON> via e-mail i risultati"}, "reset": "Reset", "confirm": "Aggiorna & Valida"}, "instructions": {"title": "Istruzioni", "content": "<p><PERSON><PERSON> tootl valida gli input usati nel caricamento iniziale di TAM4.</p>\n<ul>\n<li>Attenzione, potrebbe impiegare <b>un po di tempo</b> a completare la validazione dei file a seconda della loro dimensione</li><li>In caso di errori, puoi copiare il log sottostante per comunicarli a cliente</li><li>Sono accettati solo file compressi (*.ZIP)</li><li>Il file compresso <b>NON DEVE</b> contenere cartelle</li></ul>"}, "steps": {"upload": "Caricamento completato &mdash; Validazione in corso.", "uploading": "Caricamento in corso &mdash; Non chiudere questa pagina.", "outcome": {"title": "Risultato", "success": "Il file rispetta il formato concordato", "warning": "Il file rispetta il formato richiesto. Vedere i suggerimenti sottostanti.", "error": "Il file non rispetta il formato richiesto. Controllare le informazioni sottostanti."}, "warnings": {"title": "Attenzione"}, "errors": {"title": "<PERSON><PERSON><PERSON>"}}, "error": "Si è verificato un errore durante l'elaborazione della richiesta"}, "export": {"title": "Corpus Export", "corpus": {"download": {"title": "Scarica l'ultimo corpus generato disponibile", "button": "Scarica"}, "generate": {"error": "Si è verificato un errore durante la generazione del corpus", "title": "Genera il file corpus usando i dati sul database", "button": "Genera", "ongoing": "Generazione del corpus, attendere prego", "stop": "Ferma processo"}}}, "ontology-management": {"form": {"panel": "Aggiorna l'ontologia di TAM", "title": "Per favore, caricare il/i nuovo/i file dell'ontologia", "files": {"label": "Cartella compressa (*.zip)", "placeholder": "Seleziona il file"}, "reset": "Resetta"}, "instructions": {"title": "Istruzioni", "content": "<p>In questa pagina, è possibile aggiornare l'ontologia di TAM4 ontology e pubblicarla sui vari servizi.</p>\n<ul>\n<li>Considerare che può richiedere <b>un po'</b> di tempo, a seconda della dimensione del file.</li><li>In caso di errore, sentitevi liberi di copiare il log di errore e comunicarli ai responsabili</li><li>Caricare solo file con estensione .zip</li><li>Assicurarsi che il file zip <b>non</b> contenga cartelle.</li></ul>"}, "upload": {"upload-button": "Carica", "uploading": "File in caricamento &mdash; Non chiudere questa pagina.", "success": "The file conforms to the required format.", "error": "Il file non rispetta il formato richiesto. Leggere sotto per maggiori informazioni.", "connection-error": "Errore sconosciuto durante la connessione."}, "publish": {"publish-button": "Pubblica", "success": "Ontologia pubblicata correttamente.", "error": "Impossibile publiccare l'ontologia. Leggere sotto per maggiori informazioni.", "publishing": "In pubblicazione"}, "warnings": {"title": "Avvisi"}, "errors": {"title": "<PERSON><PERSON><PERSON>"}, "error": "Errore processando la richiesta"}, "errors": {"missing-client-code": "Codice client mancante"}, "select-client": "Seleziona un client di caricamento dati per mostrare i grafici!", "validationload": {"form": {"panel": "Convalida dei file cliente", "title": "Per favore compila questo modulo", "tableFiles": {"label": "File tabella CSV (*.zip)", "placeholder": "Seleziona file", "confirm": "<PERSON><PERSON>"}, "ruleFiles": {"label": "File regole (*.json)", "placeholder": "Seleziona file", "confirm": "Carica <PERSON>ole", "lastUploadedRuleFile": "Data ultimo aggiornamento file regole"}, "email": {"label": "Email", "placeholder": "Indirizzo e-mail della tua azienda"}, "scope": {"label": "<PERSON><PERSON>", "placeholder": "ad esempio, caricamento completo, aggiornare IT, ..."}, "notify": {"label": "<PERSON><PERSON><PERSON> ricevere i risultati via e-mail"}, "reset": "Reimposta", "confirm": "Esegui Validazione", "report": "Genera rapporto", "download": "Scarica Report"}, "instructions": {"title": "Istruzioni", "content": "<p>Questo strumento carica i dati utilizzati per il caricamento iniziale di TAM4 nelle tabelle del database.</p>\n<ul>\n<li>Attenzione potrebbe impiegare <b>molto</b> tempo per il caricamento in relazione alla dimesione dei file.</li><li>In caso di errore, copiare il log qui sotto per riportare gli errori al customer</li><li>Solo i file .zip sono ammessi per il caricamento</li><li>Accertati che il tuo file zip file <b>non contenga</b> folders.</li></ul>"}, "steps": {"upload": "Caricamento completato &mdash; Caricamento nelle tabelle in corso.", "uploading": "Caricamento file &mdash; Non chiudere questa pagina.", "outcome": {"title": "Esito", "success": "I file sono conformi al formato richiesto.", "warning": "I file sono conformi al formato richiesto. Vedi sotto per suggerimenti.", "error": "Il file non è conforme con il formato richiesto. Vedere sotto per maggiori informazioni."}, "outcomeValidate": {"title": "Esito", "success": "<PERSON>tte le regole convalidate con successo.", "warning": "Tutte le regole convalidate. Vedi sotto per i suggerimenti.", "error": "Alcune regole di validazione non rispettate. Vedere sotto per maggiori informazioni."}, "warnings": {"title": "Avvert<PERSON><PERSON>"}, "errors": {"title": "<PERSON><PERSON><PERSON>"}, "uploadRules": "File delle regole caricato con successo.", "validate": "Convalida completata con successo", "validating": "Convalida in corso", "reporting": "Generazione del report in corso", "reported": "Rapporto generato, controlla la scheda Esportazione file per scaricare.", "reportGeneration": {"title": "Esito", "success": "Rapporto generato, controlla la scheda Esportazione file per scaricare.", "error": "Qualcosa è andato storto nella generazione del report"}}, "error": "Si è verificato un errore durante l'elaborazione della richiesta"}}, "duplicates": {"group": {"affected-stock": "Valore di Stock coinvolto", "total-amount-stock": "Valore totale di stock", "affected-quantity": "Quantità coinvolta", "total-amount": "Valore totale di {{value}}", "total-consumption": "Valore totale consumato", "total-consumption-value": "Valore totale consumato", "total-consumption-quantity": "Quantità totale consumata", "amount-consumed": "<PERSON><PERSON> consumato {{value}}", "confidence": "Affidabilità", "classification": "Classificazione", "accuracy-level": "Livello di precisione", "of": "di", "metric-name": "Questo gruppo è stato creato con nome e codice del produttore corrispondenti.", "send-enrichment": "<PERSON><PERSON> modifica", "assign-group-to-me": "Assegna a me", "assign-group-materials": "Assegna i materiali del gruppo per la deduplica", "hide-group": "Nascondi gruppo", "hide-group-successful": "Gruppo nascosto con successo", "show-group": "Mostra gruppo", "show-group-successful": "Gruppo mostrato con successo", "create-rel": "<PERSON><PERSON> come duplicati", "basic-data": "Dati di base", "material-code": "Codice materiale", "material-type": "Tipo materiale", "technical-attributes": "Attributi tecnici", "fields-without-values": "Campi senza valori", "description": "Descrizione", "standard-price": "Prezzo standard (EUR)", "unit-of-measure": "Unità di misura", "metric": {"Code": "Questo gruppo è stato creato con nome e codice del produttore corrispondenti.", "TechnicalSheets": "Questo gruppo è stato creato con schede tecniche corrispondenti."}, "groups-all": {"card-filter-viewed": "Stai visualizzando {{groupLength}} gruppi su {{totalGroups}} disponibili"}, "groups-exact-match": {"card-filter-viewed": "Stai visualizzando {{groupLength}} gruppi su {{totalGroups}} disponibili"}, "groups-hidden": {"card-filter-viewed": "Stai visualizzando {{groupLength}} gruppi su {{totalGroups}} disponibili"}, "assign-group-popup": {"note": "<PERSON>a", "actions": {"cancel": "<PERSON><PERSON><PERSON>", "assign": "Assegna", "loading": "Caricamento in corso ...", "no-roles": "<PERSON><PERSON><PERSON> ruolo trovato", "roles-placeholder": "Seleziona ruoli da assegnare al gruppo"}, "form-controls": {"chk-assign": "Assegna a me", "lbl-select-role": "<PERSON><PERSON><PERSON>", "info-select-role": "verrà utilizzato in caso in rimozione dell'assegnazione del gruppo."}}, "list-truncated": "Stai visualizzando 10 dei {{totalMaterials}} materiali in questo gruppo", "financial-data": "Dati economici", "affected-ordered": "Ordinato coinvolto", "display-valuations": "<PERSON>ra valori", "customization": "Personalizzazione", "std-price-from-to": "da {{from}} to {{to}}", "assign-group": "Assegna per deduplica", "shelve-group": "Nascondi gruppo", "shelve-group-successful": "Gruppo nascosto con successo", "dismiss-group": "Dismetti gruppo", "dismiss-group-successful": "Gruppo dismesso con successo", "confirm": "Tutto il gruppo verrà rimosso dalla lista dei potenziali duplicati, indipendentemente dai filtri impostati (es.: client, category)", "cancel": "<PERSON><PERSON><PERSON>", "confirmation-message": "L'intero gruppo verrà rimosso dalla lista dei potenziali duplicati, indipendentemente dai filtri (es. client, categoria)", "locked": "Si prega di ignorare questo gruppo se hai già completato il tuo lavoro su di esso (lo status è in aggiornamento)", "total-ordered": "Ordini di acquisto totali", "total-ordered-value": "Valore totale degli ordini di acquisto", "total-ordered-quantity": "Quantità totale ordini di acquisto", "display-label": "Mostra/nascondi blocco", "display-manufacturer": "<PERSON><PERSON>", "display-customer-fields": "Campi custom", "display-technical-attributes": "Attributi", "plants": "Divisioni", "plants-more": "Mostra di più ({{numberOfPlants}})", "countries": "<PERSON><PERSON>", "countries-more": "Mostra altri ({{numberOfCountries}})", "description-show": "Mostra Altro", "description-hide": "Nascondi Altro", "descriptions": {"shortDescriptions": "Breve descrizione.", "purchaseOrderDescriptions": "Descrizione Ordine d'Acquisto.", "internalNoteDescriptions": "Descr. Nota Interna", "longDescriptions": "Descr. <PERSON>", "inspectionDescriptions": "Descr. Ispezione"}, "customer-fields": "Campi custom", "manufacturer-data": "Dati del produttore", "manufacturer-part-number": "Numero di parte del produttore", "no-plants": "<PERSON><PERSON><PERSON> impianto"}, "messages": {"group-hidden-successful": "Gruppo duplicati nascosto con successo", "group-hidden-failure": "Non è stato possibile nascondere il gruppo duplicati selezionato"}, "errors": {"error-message": "Oops! Qualcosa è andato storto...", "error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "cannot-lock-group": "L'operazione non è permessa, un'altro utente sta lavorando sullo stesso gruppo", "workflow": {"errors": {"cannot-start-process-materials-already-in-deduplication": "Impossibile avviare il processo. Uno o più masterdata sono attualmente coinvolti in un processo di deduplica"}}}, "empty": {"numberOfGroupsExactMatch": {"title": "<PERSON><PERSON> corrispondente ai gruppi vuoti", "subtitle": "So<PERSON><PERSON><PERSON>lo corrispondente ai gruppi vuoti."}, "numberOfGroups": {"title": "Nessuna task in coda", "subtitle": "Quando un data manager assegna materiali ad una delle tue divisioni, lo vedrai qui"}, "numberOfHiddenGroups": {"title": "Nessuna attività in attesa di approvazione", "subtitle": "Quando completi una delle task che ti sono state assegnate, apparir<PERSON> qui"}}, "home": {"numberOfGroupsExactMatch-filtered": {"title": "Gruppi di potenziali duplicati trovati", "subtitle": "Gruppi con solo materiali che corrispondono ai filtri"}, "numberOfGroupsExactMatch": {"title": "Gruppi di potenziali duplicati", "subtitle": "Elenco di gruppi di potenziali duplicati"}, "numberOfGroups": {"title": "Gruppi di potenziali duplicati trovati", "subtitle": "Gruppi di materiali che corrispondono parzialmente ai filtri"}, "numberOfHiddenGroups": {"title": "Gruppi nascosti", "subtitle": "Gruppi nascosti"}, "numberOfHiddenDueToProcessGroups": {"title": "Gruppi con lavori in corso", "subtitle": "Gruppi con materiali che hanno un processo attivo"}}, "confirmation": {"yes": "Si", "no": "No"}, "pagination": {"first": "Vai alla prima pagina", "previous": "Vai alla pagina precedente", "next": "Vai alla pagina seguente", "last": "Vai all'ultima pagina"}, "file-export": {"launch-file-export": "Esporta", "launch-file-export-successful": "Richiesta di esportazione generata"}, "search": {"no-records-found": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato.", "no-matching-global-filters-results": "Non ci sono risultati corrispondenti alla corrente combinazione di filtri.", "suggestions": "<PERSON><PERSON><PERSON><PERSON>", "filter-combination-sugg": "Prova una combinazione di filtri diversa", "diff-search-terms-sugg": "Prova un codice differente", "general-search-sugg": "Prova ad utilizzare termini più generici", "reset-filters-sugg": "Prova a resettare i filtri"}, "filters": {"clear-filters": " <PERSON><PERSON><PERSON><PERSON>", "metrics": "Filtra gruppi per Metrica", "confidence": "Filtra gruppi per affidabilità", "order-by": "Ordina gruppi per", "default-value": "Q<PERSON>un<PERSON>", "default-order": "Predefinito", "stock-amount": "Valore totale di Stock", "consumption-amount": "Valore totale consumato", "confidence-values": {"GOOD": "BUONO", "FAIR": "DISCRETO", "POOR": "SCARSO"}, "consumption-amount-asc": "Ammontare Consumato (Crescente)", "materials": "Materiale(i)", "materials-placeholder": "Digita il codice materiale (premi invio per aggiungere)", "material-list": "Elenco Materiali"}, "amounts": {"materials": "Materiali", "stock": "Valore totale di Stock", "consumption": "Valore totale consumato", "ordered": "Valore totale ordinato", "services": "servizi"}, "popups": {"delete-relationship": {"confirmation": {"header": "Attenzione: Possibile Perdita di Dati", "message": "Se procedi, tutte le modifiche non salvate nella sezione precedente saranno perse. Sei sicuro di voler continuare?", "acceptLabel": "Sì", "rejectLabel": "No"}}}}, "folder-navigation": {"folder-list": {"node": "Nodo", "stock": "Stock", "consumption": "<PERSON><PERSON><PERSON>", "ordered": "<PERSON><PERSON><PERSON>", "row-count": "Anagrafiche", "total": "Totale", "page-empty": {"title": "Nessuna folder definita", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "country-chart": {"title": "Distribuzione per paese", "table": {"code": "Codice", "row-count": "Numero di oggetti"}, "page-empty": {"title": "Le anagrafiche in questa categoria non sono estese su alcuna divisione", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "plant-chart": {"title": "Distribuzione per Divisione", "page-empty": {"title": "Le anagrafiche in questa categoria non sono estese su alcuna divisione", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "attribute-chart": {"title": "Distribuzione degli Attributi Tecnici", "page-empty": {"title": "Non ci sono attributi tecnici definiti per questa categoria", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "pagination": {"first": "Primo", "previous": "Precedente", "next": "Successivo", "last": "Ultimo"}, "errors": {"error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Qualcosa è andato storto, per favore riprova più tardi"}, "search": {"title": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "subheading": "Non ci sono risultati che soddisfano i tuoi filtri:", "suggestion-title": "Suggerimenti:", "line1": "Prova una combinazione di filtri differente", "line2": "Prova a resettare i filtri"}, "breadcrumb": {"categories": "categorie"}, "actions": {"set-as-filter": "Imposta filtro"}, "master-data-type-chart": {"title-materials": "Distribuzione per tipo materiale", "title-services": "Distribuzione per tipo servizio", "page-empty": {"title": "Le anagrafiche in questa categoria non hanno alcun tipo materiale definito", "subtitle": "subtitle"}}}, "horizontal-menu": {"menu-1": {"label": "Esempio", "title": "<PERSON><PERSON>"}, "pageTitle": {"home-page": "Pagina iniziale", "search": "Ricerca", "duplicates-management": "Gestione dei duplicati", "worklists": "Elenco attività", "enrichment": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifica", "creation": "Creazione", "approver": "Approvazione", "relationship": "Relazioni", "administration": "Amministrazione", "data-quality": "Qualità dei dati", "worklist-duplicates": "<PERSON><PERSON><PERSON><PERSON>", "worklist-extension": "Estensione Divisioni", "worklist-relationship": "Relazioni", "folder-navigation": "Esplora i folder", "smart-creation": "Smart creation", "reporting": "Reportistica", "reporting-materials": "Materiali", "reporting-workflow": "Workflow", "reporting-warnings": "Avviso", "engine-warnings": "Warning", "reporting-relationship": "Relazioni", "file-export": "Esportazione File", "events-store": "Archivio eventi", "data-loader": "Caricamento dati", "contracts": "<PERSON><PERSON><PERSON>", "suppliers": "Fornitore", "excel-integration": "Integrazione Excel", "warnings-dashboard": "Gestione avvisi", "account-management": "Gestione utenti", "business-units-admin": "Gestione unità di business", "clients-administrator": "Gestione mandanti", "country-admin": "Gestione paesi", "country-region-admin": "Gestione paesi/ regioni", "custom-roles-admin": "Gestione ruoli personalizzati", "group-management": "Gestione gruppi", "plant-admin": "Gestione divisioni", "region-admin": "Gestione regioni", "role-management": "Gestione ruoli", "slim-account-management": "Gestione Account Utente", "audit": "Audit", "bulk-upload": "Upload di massa", "changelog": "Registro Modifiche", "ontology-management": "Gestione ontologia", "scheduled-reports": "Report schedulati", "supplier-portal": "Portale Fornitori", "supplier-to-upload": "Caricamento", "events-storage": "Archivio eventi", "integration-monitor": "Monitor d'integrazione", "massive-edit": "Edit Massivo", "data-loader-report": "Report Data Loader", "manage-suppliers": "Gestione fornitori", "base": "Base", "bulk-extension": "Bulk Extension", "starter": "Starter", "validation": "Validation", "analytics": "Statistiche", "admin-tools": "Strumenti di Amministrazione", "attribute-mapper-admin-tool": "Strumento di Amministrazione della Mappatura degli Attributi", "change-doc-classification": "Può cambiare le categorizzazioni", "workflow-processes-monitor": "Monitor del Processo di Workflow", "can_propose_new_values": "<PERSON><PERSON><PERSON> proporre nuovi valori", "can_manage_golden_record": "Può gestire il Golden Record", "can-upload-custom-template": "Può caricare un modello personalizzato", "massive-relationship": "Relazionamento Massivo", "batch-api": "API batch", "external-gateway-api": "API del Gateway Esterno", "data-loader-api": "API di caricamento dati", "internal-api": "API interna", "organizations-management": "Gestione delle organizzazioni"}, "pageSubTitle": {"home-page": "Pagina iniziale", "search": "Ricerca testo", "duplicates-management": "Gestione dei duplicati", "worklists": "Elenco attività", "enrichment": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifica", "creation": "Creazione", "approver": "Approvazione", "relationship": "Relazioni", "administration": "Amministrazione", "data-quality": "Qualità dei dati", "worklist-extension": "Estensioni di divisione", "worklist-duplicates": "<PERSON><PERSON><PERSON><PERSON>", "folder-navigation": "Esplora i dati del sistema", "smart-creation": "Smart creation", "reporting": "Reportistica", "reporting-materials": "Materiali", "reporting-workflow": "Workflow", "reporting-warnings": "Avviso", "engine-warnings": "Warning", "reporting-relationship": "Relazioni", "file-export": "Esportazione File", "events-store": "Archivio eventi", "data-loader": "Caricamento dati", "contracts": "Elenco di lavoro <PERSON>tti", "suppliers": "Lista fornitori", "excel-integration": "Integrazione Excel", "warnings-dashboard": "Gestione avvisi", "account-management": "Gestione utenti", "business-units-admin": "Gestione unità di business", "clients-administrator": "Gestione mandanti", "country-admin": "Gestione paesi", "country-region-admin": "Gestione paesi/ regioni", "custom-roles-admin": "Gestione ruoli personalizzati", "group-management": "Gestione gruppi", "plant-admin": "Gestione divisioni", "region-admin": "Gestione regioni", "role-management": "Gestione ruoli", "slim-account-management": "Gestione account utente", "audit": "Revisione", "bulk-upload": "Upload di massa", "changelog": "Registro Modifiche", "scheduled-reports": "Report schedulati", "bulk-extension": "Estensione Massiva", "events-storage": "Archivio eventi", "integration-monitor": "Monitor d'integrazione", "massive-edit": "Edit Massivo", "data-loader-report": "Report Data Loader", "manage-suppliers": "Gestione fornitori", "starter": "Starter", "validation": "Validation", "analytics": "Statistiche", "admin-tools": "Strumenti di Amministrazione", "supplier-portal": "Portale Fornitori", "ontology-management": "Gestione ontologia", "attribute-mapper-admin-tool": "Strumento interno di gestione delle categorie", "change-doc-classification": "Può cambiare le categorizzazioni", "workflow-processes-monitor": "Monitor del Processo di Workflow", "can_propose_new_values": "<PERSON><PERSON><PERSON> proporre nuovi valori", "can_manage_golden_record": "Può gestire il Golden Record", "can-upload-custom-template": "Può caricare un modello personalizzato", "massive-relationship": "Relazionamento Massivo"}}, "header": {"toggle-aside-menu": "<PERSON><PERSON><PERSON>"}, "top-bar": {"select-language": "Seleziona la tua lingua", "change-language": "Cambia lingua", "profile-menu": "<PERSON><PERSON> profilo", "notifications": "Notifiche utente", "empty": "Nessuna notifica", "logout": "<PERSON><PERSON><PERSON>"}, "aside": {"home": "Dashboard"}, "lang": {"it": "Italiano", "en": "<PERSON><PERSON><PERSON>", "es": "<PERSON><PERSON><PERSON>", "de": "Tedesco", "pt": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON>", "ru": "<PERSON>", "zh": "Cinese", "ko": "<PERSON><PERSON>", "ro": "R<PERSON>no", "gr": "Greco", "pl": "Polacco", "cs": "Ceco", "hr": "<PERSON><PERSON><PERSON><PERSON>", "iw": "Ebraico", "cz": "Ceco", "sq": "Albanese", "ar": "<PERSON><PERSON>", "be": "<PERSON><PERSON><PERSON> (Fiammingo)", "bg": "Bulgaro", "ca": "<PERSON><PERSON>", "da": "<PERSON><PERSON>", "nl": "<PERSON><PERSON><PERSON>", "et": "Estone", "fi": "Finlandese", "el": "Greco", "hi": "<PERSON><PERSON>", "hu": "<PERSON><PERSON><PERSON><PERSON>", "is": "Islandese", "ja": "Giapponese", "lv": "Let<PERSON>", "lt": "Lituano", "mk": "Macedone", "ms": "Malese", "mt": "Maltese", "no": "Norvegese", "nb": "Norvegese (Bokmål)", "nn": "Norvegese (Nynorsk)", "sk": "Slovacco", "sl": "Sloveno", "sr": "<PERSON><PERSON>", "sv": "<PERSON><PERSON><PERSON>", "th": "Tailandese", "tr": "<PERSON><PERSON><PERSON>", "uk": "Ucraino", "vi": "Vietnamita"}, "search-popup": {"favourite": "Preferiti", "search": "Ricerca", "code": "Codice", "description": "Descrizione", "loading": "Caricamento...", "keep-open": "Resta aperto", "apply": "Applica", "error-message": "Oops! Qualcosa è andato storto.", "empty-search": "<PERSON><PERSON><PERSON> r<PERSON> trovato"}, "layout": {"errors": {"unknown-error": "Si è verificato un errore sconosciuto", "title": "L'operazione non può essere completata", "warning": "Attenzione"}, "success": {"success-compited": "Operazione completata con successo", "take-in-charge": "Operazione presa in carico"}, "home-page": {"menu-item": {"material-count": {"title": "Materiali", "subtitle": "Quantità totale di materiali"}, "total-stock": {"title": "Stock totale", "subtitle": "Stock totale in tutti i paesi"}, "total-consumption": {"title": "Consumo totale", "subtitle": "Consumo totale in tutti i paesi"}, "your-requests": {"title": "Le tue richieste", "subtitle": "Conteggio totale delle tue richieste attive"}, "warnings": {"title": "Avviso", "subtitle": "Ci sono oggetti che richiedono la tua attenzione"}}, "enabled-features": "Funzioni Abilitate", "enabled-worklist-features": "Funzioni abilitate per la lista attività", "enabled-administrative-features": "Funzioni amministrative abilitate", "current-processes-overview": "Panoramica dei processi correnti", "task-card-title": "Attività attualmente in coda", "task-card-overview": "Panoramica attività", "last-updated-on": "Ultimo <PERSON>rnamento:", "material-code": "Codice materiale:", "see-more": "Mostra di più...", "see-less": "Mostra meno...", "empty-tasks": {"title": "Non hai attività in coda", "subtitle": "Non hai attività in coda. Quando i data manager assegneranno elementi a una delle tue divisioni, li vedrai qui."}, "task-status": {"additional-enrichment-request": "Richiesta di ulteriore arricchimento", "additional-edit-request": "Richiesta di ulteriore arricchimento", "approved": "A<PERSON>rovato", "cancelled": "<PERSON><PERSON><PERSON>", "completed": "Completati", "done": "<PERSON><PERSON>", "draft": "<PERSON><PERSON>", "erp-error": "Errore ERP", "erp-updated": "ERP aggiornato", "feedback-received": "Feedback ricevuto", "in-progress": "In corso", "integration-errors": "Errori di integrazione", "in-your-queue": "In coda", "rejected": "Rifiutato", "requires-more-information": "Richiede ulteriori informazioni", "submitted": "Inviato", "waiting-for-approval": "In attesa di approvazione", "waiting-for-enrichment": "In attesa di arricchimento", "waiting-for-edit": "In attesa di modifica", "waiting-for-erp": "In attesa dell'ERP", "waiting-for-external-system": "In attesa del sistema esterno", "waiting-for-feedback": "In attesa di risposta", "waiting-for-handling": "In attesa di gestione", "waiting-for-manual-handling": "In attesa della gestione manuale", "waiting-for-starting": "In attesa di avvio", "waiting-for-apply": "In attesa", "aborted": "Abbandonato"}}, "relationship-popup": {"choose-relationship-types": "Scegli uno dei seguenti tipi di relazione", "new-relationship": {"duplicate": "Crea una nuova relazione di duplicazione", "equivalence": "Crea una nuova relazione di equivalenza", "interchangeable": "Crea una nuova relazione di intercambiabilità", "noRelationship": ""}, "relationship-type": "Tipo di relazione", "primary-material": "Seleziona materiale primario", "materials-code": "Codice materiale", "services-code": "Codice servizio", "material-description": "Descrizione", "material-stock-amount": "Valore totale di stock", "material-consumption-amount": "Valore totale consumato", "material-order-amount": "Valore totale ordinato", "note": "Note", "button-cancel": "<PERSON><PERSON><PERSON>", "button-back": "Indietro", "button-request-creation": "<PERSON><PERSON> creazione", "button-next": "Continua", "button-confirm-selection": "Conferma relazione", "relationship-types": {"duplicate": "Dup<PERSON><PERSON>", "duplicate-label-materials": "Parti che sono completamente intercambiabili nella pianificazione, i materiali secondari potranno diventare obsoleti", "duplicate-label-services": "Parti che sono completamente intercambiabili nella pianificazione, i servizi secondari potranno diventare obsoleti", "duplicate-secondary-materials": "I materiali marcati come secondari in una relazione di tipo duplicato non appariranno in gestione duplicati.", "duplicate-secondary-services": "I servizi marcati come secondari in una relazione di tipo duplicato non appariranno in gestione duplicati.", "equivalence": "Equivalenza", "equivalence-label-materials": "Parti che sono completamente intercambiabili nella pianificazione, gestione delle scorte e disponibilità. Creati tra materiali di uguale applicazione. Tutti i materiali sono matenuti nel sistema.", "equivalence-label-services": "Parti che sono completamente intercambiabili nella pianificazione, gestione delle scorte e disponibilità. Tutti i servizi sono matenuti nel sistema.", "interchangeable": "Intercambiabilità", "interchangeable-label-materials": "Parte intercambiabile a senso unico nella pianificazione, inventario e disponibilità. È stabilito tra articoli in cui uno può sostituire un altro, ma non viceversa. Tutti i materiali sono matenuti nel sistema.", "interchangeable-label-services": "Parte intercambiabile a senso unico nella pianificazione, inventario e disponibilità. Tutti i servizi sono matenuti nel sistema.", "norelationship": "", "norelationship-label-materials": "", "norelationship-label-services": ""}, "equivalence-note-materials": "<PERSON><PERSON>li il materiale che sarà contrassegnato come equivalente", "equivalence-note-services": "Scegli il servizio che sarà contrassegnato come equivalente", "materials-note": "<PERSON>egli un materiale come primario facendo clic su ", "services-note": "Scegli un servizio come primario facendo clic su ", "relationship-note": {"duplicate": "Spiega perché stai creando questa relazione di duplicazione", "equivalence": "Spiega perché stai creando questa relazione di equivalenza", "interchangeable": "Spiega perché stai creando questa relazione di intercambiabilità", "norelationship": "<PERSON><PERSON><PERSON> perché stai creando questa relazione di no-relationship"}, "steps": {"step1": "Specifica il tipo", "step2-materials": "Scegli i materiali", "step2-services": "Scegli i servizi", "step3": "Revisione"}, "success-messages": {"relationship-requested": "Relazione richiesta"}, "md-statuses": "Stati delle anagrafiche", "client": "<PERSON><PERSON><PERSON>", "only-golden-record": "Attenzione: solo i processi di creazione golden record verranno creati in quanto non ci sono abbastanza elementi per l'inizio di un processo di relazione", "create-golden-record": "<PERSON>rea golden record", "golden-record": "Golden Record", "errors": {"select-only-one": "Client {{client}}: si prega di selezionare un master data o selezionare un solo item", "golden-record-no-slave": "Client {{client}}: si prega di rimuove il golden record dalla relazione di dipendenza", "no-golden-record": "Client {{client}}: si prega si deselezionare i golden records dalla lista o deselezionare l'opzione per crearne uno nuovo.", "missing-primary": "Mandante {{client}}: selezionare un materiale primario", "missing-instance": "Si prega di selezionare almeno una istanza di item (primaria o secondaria) per poter creare il Golden Record", "missing-primary-client": "Si prega di selezionare un item primario", "existing-process": "C'è già un processo di creazione di una relazione in corso: "}, "primary": "Primario", "secondary": "Secondario", "interchangeable": "Intercambiabile", "equivalent": "Equivalente", "deletion-relationship-note": {"duplicate": "Si richiede di eliminare la seguente relazione duplicata", "equivalence": "Si richiede di eliminare la seguente relazione di equivalenza", "interchangeable": "Si richiede di eliminare la seguente relazione intercambiabile"}}, "assignment-popup": {"new-assignment": "Crea una nuova attività di modifica", "material-details": "<PERSON><PERSON><PERSON>", "material-code": "Codice materiale", "material-description": "Descrizione", "note": "Spiega perché stai creando queste attività", "plants-grouping": "Gruppo # {{value}}", "button-cancel": "<PERSON><PERSON><PERSON>", "button-confirm": "Conferma la richiesta di modifica", "button-confirm-additional-edits": "<PERSON>di ulteriori modifiche", "invalid-combinations": "Gruppi: {{value}} non ha divisioni selezionati. Si prega di selezionare almeno una divisione.", "errors": {"error-message": "Oops! Qualcosa è andato storto.", "cannot-perform-assignment": "Non è possibile assegnare in questo momento"}, "additional-edit": {"material-details": "<PERSON><PERSON><PERSON>", "material-code": "Codice materiale", "requester-note": "Nota del richiedente", "header-info": "{{assignedCount}} fuori da {{total}} i materiali sono stati assegnati con successo per la modifica. Il resto {{remainingCount}} dei materiali sono stati ignorati perché hanno già una richiesta di modifica associata.", "header-info-all-missed": "Tutti i materiali sono stati ignorati perché hanno già una richiesta di modifica associata."}, "material-quality": "Qualità", "material-assign-on-plants": "Assegna alle divisioni"}, "plant-extension-popup": {"material-plant-extension": "Stai richiedendo l'estensione per il codice materiale", "request-plant-extension-materials": "Stai richiedendo l'estensione per il codice materiale", "request-plant-extension-services": "Stai richiedendo l'estensione per il servizio", "actions": {"actions": "Azioni", "add-plant": "Aggiungi divisione", "save-plant": "Salva divisione", "add-more": "Aggiungi altro", "cancel-request": "<PERSON><PERSON>a richiesta", "request-extension": "Richiedi estensione divisione", "loading": "Caricamento in corso ...", "no-data": "<PERSON><PERSON><PERSON> articolo trovato", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "go-to-request": "Vai alla richiesta", "confirm-popup": {"delete": {"title": "Conferma per cancellare", "message": "Sei sicuro di voler eliminare la richiesta?"}, "cancel": {"title": "Sei sicuro di voler annullare la tua richiesta?", "message": "Se annulli la richiesta tutte le richieste verranno eliminate"}, "change": {"title": "Sei sicuro di voler modificare il master data?", "message": "Se cambi il master data, tutte le estensioni non completate per il materiale corrente verranno perse"}, "delete-golden-record": {"title": "Confermare per cancellare", "message": "Il Golden Record verrà cancellato e tutte le sue istanze verranno scollegate. I dati delle istanze rimarranno inalterati"}, "delete-golden-record-response": {"deleted-successfully": "Il Golden Record è stato cancellato con successo", "pending-processes": "Ci sono ancora processi in corso"}, "processes-pending": {"title": "Processo in corso", "message": "Questo golden record ha un processo di modifica in corso. Confermando l'eliminazione, il processo di modifica verrà interrotto."}}, "open-details": "Dettaglio Item", "confirmation-message": "I cambiamenti effettuati al masterdata sorgente verranno eliminati", "alert-button-collapse-true": "", "alert-button-collapse-false": ""}, "form": {"plant-information": "Informazioni relative alle divisioni", "planing-information": "Informazioni logistiche", "masterdata-valuation": "Valutazione dei master data", "storage-locations": "<PERSON><PERSON><PERSON><PERSON>", "plant-code": "Codice divisione", "plant-description": "Descrizione della divisione", "mrp-group": "Gruppo MRP", "mrp-type": "Tipo MRP", "mrp-controller": "Controllo MRP", "purchasing-group": "Gruppo d'acquisto", "logistics-group": "Gruppo logistico", "lead-time-in-days": "Tempi di consegna (in giorni)", "reorder-point": "Punto di riordino", "safety-stock": "Stock di sicurezza", "min-safety-stock": "Stock minimo di sicurezza", "max-stock-level": "<PERSON><PERSON> di magazzino massimo", "standard-price": "Prezzo standard:", "price-unit": "Prezzo/Unità", "lot-size": "Dimensione del lotto", "min-lot-size": "Dimensione minima del lotto", "multiple-valuations": "Supporta valutazioni multiple", "currency": "Valute", "available-storages": "Magazzini disponibili", "master-data-status": "Stato del Master Data", "previous-value": "Previous value:", "origin-material": "Origin material", "intrastat-code": "Codice Intrastat", "control-code-consumption-taxes-foreign-trade": "Codice di controllo Tasse sui consumi Commercio estero", "cfop-category": "Categoria CFOP dei Materiali", "period-indicator": "Indicatore del Periodo", "special-procurement-type": "Tipo speciale acquisto", "group-availability-check": "Controllo della disponibilità in corso", "valuation-class": "Classe di Valutazione", "moving-average-price": "Prezzo Medio Mobile", "price-control-indicator": "Indicatore di Controllo del prezzo", "usage-material": "<PERSON><PERSON><PERSON>zo <PERSON>", "other-attributes": "Altri attributi", "other-attributes-logistic": "Altri Attributi di logistica", "plant-data-status": "Stato dei dati del Plant", "other-attributes-master-data-valuations": "Altre Valutazioni Master Data", "profit-center": "Centro di profitto", "plant-old-material-number": "Vecchio numero materiale del Plant", "lot-size-for-product-costing": "Dimensione del lotto per il calcolo del costo del prodotto", "no-storage-bins": "<PERSON><PERSON><PERSON> contenitore di stoccaggio", "storage-location": "Ubicazione di Magazzino", "storage-bin": "Ubicazione di stoccaggio", "storage-bins": "Ubicazioni di stoccaggio", "applied-edits": "Modifiche applicate", "attribute": "Attributo", "edit-type": "Tipo di modifica", "current-value": "<PERSON><PERSON> corrente", "certificate-type": "Tipo di certificato", "requester-note": "Nota del richiedente", "fixed-lot-size": "Dimensione Lotto Fissa", "procurement-type": "Tipo di approvvigionamento", "ordering-costs": "Costi di ordinazione", "storage-costs-indicator": "Indicatore dei costi di stoccaggio", "rounding-value-for-purchase-order-quantity": "Valore di arrotondamento per la quantità dell'ordine d'acquisto", "unit-of-issue": "Unità di prelievo", "strategy-group": "Gruppo strategico", "critical-part": "Parte Critica", "effective-out-date": "Data di fine validatà", "country-of-origin": "Paese di origine", "loading-group": "Gruppo di carico", "planning-time-fence": "Perimetro di pianificazione temporale", "control-code-for-consumption-taxes-in-foreign-trade": "Codice di Controllo per le Imposte sui Consumi nel Commercio Estero", "consumption-mode": "Modalità di Consumo", "consumption-period-backward": "Periodo di consumo: all’indietro", "consumption-period-forward": "Periodo di consumo: in avanti", "follow-up-material-code": "Codice Materiale Succesivo", "serial-number-profile": "Profilo Numero di Serie", "maintenance-status": "Stato di manutenzione", "max-lot-size": "Dimensione massima lotto", "draft-status": "", "no-value": ""}, "errors": {"material-is-extended": "Il materiale è già stato esteso alla divisione selezionato, selezionane un altro.", "material": {"empty-description": "Si prega di compilare i seguenti campi e riprovare:", "description-length": "Le seguenti descrizioni superano la lunghezza massima (40 cifre):"}}, "client": "Client", "found-duplicates": "Trovati duplicati per il client: {{client}}", "no-selection": "Seleziona un client differente", "instance-already-exist": {"title": "Attenzione", "content": "Per estendere questo materiale sui plant, devi usare la seguente istanza"}, "no-gr-instance": "<PERSON>essun golden record trovato", "start-gr-creation": "Inizia processo di creazione golden record", "table": {"edit": "Modifica", "code": "Codice", "description": "Descrizione", "country": "<PERSON><PERSON>", "extend-me": "Estendi questo item"}, "no-duplicates-found": "<PERSON><PERSON><PERSON> duplicato trovato per il client: {{client}}", "request-gr-creation": {"new-gr": "una nuova creazione di golden record", "new-masterdata-with-plants": "una nuova creazione di Master Data per client, estesa sui plant specificati", "new-masterdata-client": "una nuova creazione di Master Data sul client target", "plant-extension-request": "una richiesta di estensione del plant sul plant specificato"}, "no-languages-available": "Nessuna lingua associata a questo plant", "plant-status-changed": "Modificato", "confirm-extension-warning": "La conferma di questo farà scattare l'estesnione", "plant-extension-successful": "Estensione dell'impianto riuscita"}, "plant-update-popup": {"material-plant-update": "Stai richiedendo un aggiornamento per le seguenti divisioni, sul codice materiale ...", "request-plant-update": "Richiedi un aggiornamento delle divisioni", "actions": {"actions": "Azioni", "edit-plant": "Modifica divisione", "update-plant": "Aggiorna divisione", "cancel-request": "Annullare la richiesta", "request-update": "Richiedi aggiornamento delle divisioni", "loading": "Caricamento in corso ...", "no-data": "<PERSON><PERSON><PERSON> articolo trovato", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "confirm-popup": {"delete": {"title": "Conferma per cancellare", "message": "Sei sicuro di voler eliminare la richiesta?"}, "cancel": {"title": "Sei sicuro di voler annullare la tua richiesta?", "message": "Se annulli la richiesta tutte le richieste verranno eliminate"}}}, "plant-update-successful": "Richiesta di aggiornamento impianto creata"}, "plant-approval-popup": {"approve-extension": "Approva estensione divisioni", "approve-update": "Approva aggiornamento divisioni", "approve-relationship": "Approva relazione", "approval-note": "Scrivi il motivo della tua decisione", "form": {"plant-information": "Informazioni relative alla divisione", "planing-information": "Informazioni logistiche", "masterdata-valuation": "Valutazione dei master data", "storage-locations": "<PERSON><PERSON><PERSON><PERSON>", "plant-code": "Codice divisione", "plant-description": "Descrizione della divisione", "mrp-group": "Gruppo MRP", "mrp-type": "Tipo MRP", "mrp-controller": "Controllo MRP", "material-status": "Stato del materiale", "purchasing-group": "Gruppo d'acquisto", "logistics-group": "Gruppo logistico", "lead-time-in-days": "Tempi di consegna (in giorni)", "reorder-point": "Punto di riordino", "safety-stock": "Stock di sicurezza", "min-safety-stock": "Stock minimo di sicurezza", "max-stock-level": "<PERSON><PERSON> di magazzino massimo", "standard-price": "Prezzo standard", "price-unit": "Prezzo/Unità", "min-lot-size": "Dimensione minima del lotto", "multiple-valuations": "Supporta valori multipli", "currency": "Valute", "available-storages": "Magazzini disponibili"}, "actions": {"back-to-overview": "Ritorna alla panoramica", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "reject": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>"}, "confirm-popup": {"title": "Sei sicuro di voler cancellare la richiesta?", "message": "Se cancelli la richiesta, tutte le richieste verranno cancellate"}, "approve-extension-for-material-code": "Approva estensione divisioni per materiale con codice", "approve-update-for-material-code": "Approva aggiornamento divisioni per materiale con codice", "extension-process-details-for-material-code": "Estensione dettagli di processo per materiale con codice", "update-process-details-for-material-code": "Aggiornamento dettagli di processo per materiale con codice"}, "material-history-popup": {"modal-title": "Storico del materiale", "active-processes": "<PERSON>i attivi", "completed-processes": "Processi completati", "open-details": "Apri i dettagli per", "no-data": {"header": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "description": "Nessun registro di revisione trovato per questo materiale"}, "open-process-details": "<PERSON>i dettagli per", "completed-process": {"material_created": "<PERSON><PERSON><PERSON>", "material_updated": "Aggiornato", "material_relationship_created": "Relazione aggiunta", "material_relationship_updated": "Relazione aggiornata", "material_relationship_deleted": "Relazione cancellata", "material_warning_managed": "Avviso elaborato", "material_extension_created": "<PERSON><PERSON><PERSON>", "material_extension_edited": "Estensione modificata", "material_deduplicated": "Deduplicato", "material_extension_imported": "Estensione importata", "material_creation_imported": "<PERSON><PERSON>rta<PERSON>", "CREATION": "<PERSON><PERSON><PERSON>", "UPDATE": "Aggiornato", "RELATIONSHIP_CREATION": "Relazione aggiunta", "RELATIONSHIP_UPDATE": "Relazione aggiornata", "RELATIONSHIP_DELETION": "Relazione cancellata", "WARNING_MANAGEMENT": "Avviso gestito", "EXTENSION_CREATION": "<PERSON><PERSON><PERSON>", "EXTENSION_EDIT": "Estensione modificata", "DEDUPLICATE": "Deduplicato", "GR_CREATION": "", "GR_UPDATE": "", "GR_SCRATCH_CREATION": ""}, "active-process": {"material_created": "In fase di creazione", "material_updated": "In fase di aggiornamento", "material_relationship_created": "Relazione in fase di aggiornamento", "material_relationship_updated": "Relazione in fase di aggiornamento", "material_relationship_deleted": "Relazione in fase di eliminazione", "material_warning_managed": "Avviso in fase di elaborazione", "material_extension_created": "In fase di estensione", "material_extension_edited": "Estensione in fase di modifica", "material_deduplicated": "In fase di deduplicazione", "CREATION": "In fase di creazione", "UPDATE": "In fase di aggiornamento", "RELATIONSHIP_CREATION": "Relazione in fase di aggiornamento", "RELATIONSHIP_UPDATE": "Relazione in fase di aggiornamento", "RELATIONSHIP_DELETION": "Relazione in fase di eliminazione", "WARNING_MANAGEMENT": "Avviso in fase di elaborazione", "EXTENSION_CREATION": "In fase di estensione", "EXTENSION_EDIT": "Estensione in fase di modifica", "DEDUPLICATE": "In fase di deduplica", "creation": "Creazione", "enrichment": "<PERSON><PERSON><PERSON><PERSON>", "fix-entity": "<PERSON>i <PERSON>", "relationship": "Relazione", "duplicates": "<PERSON><PERSON><PERSON><PERSON>", "extension": "Estensione Divisioni", "waiting-for-handling": "In attesa di gestione", "waiting-for-feedback": "In attesa di un riscontro", "erp-error": "Errore ERP", "waiting-for-enrichment": "In attesa di arricchimento", "waiting-for-approval": "In attesa di approvazione", "waiting-for-erp": "In attesa dell'ERP", "waiting-for-erp-create": "In attesa di creazione lato ERP", "waiting-for-erp-update": "In attesa di aggiornamento lato ERP", "waiting-for-job-done": "In attesa che il lavoro venga completato", "more-information-needed": "Altre informazioni necessarie", "GR_CREATION": "", "GR_UPDATE": ""}, "gr-deleted": "Golden Record Cancellato", "gr-deleted-by": "Cancellato", "instance-detached": "<PERSON><PERSON><PERSON> scollegata", "detached-from": "Istanze scollegate dal Golden Record: ", "aborted-processes": "<PERSON>i annullati", "gr-for-instance-not-found": "Nessun Golden Record trovato per l'istanza data. Potrebbe non essere ancora stato approvato/creato!"}, "process-details-popup": {"cancel": "Can<PERSON><PERSON>", "field": "Campo", "previous-value": "<PERSON><PERSON>e", "updated-value": "<PERSON><PERSON> a<PERSON>", "new-value": "Nuovo valore", "details-for": "Dettagli: ", "completed": "Completato", "in-progress": "In Corso", "start-date": "Data di inizio:", "end-date": "Data di fine:", "what-has-been-modified": "Che cosa è stato modificato?", "all-fields-modified": "Tutti i campi che sono stati modificati", "what-happened": "Che cosa è successo?", "list-of-steps": "Lista di passaggi inclusi nel procedimento", "material-details": "Dettagli materiale", "requested-material-details": "Dettagli del materiale richiesto", "relationship-details": "Dettagli relazione", "requested-relationship-details": "Dettagli relazione richiesta", "extension-details": "Crea dettagli estensione", "requested-extension-details": "Dettagli richiesta estensione", "material-plant-changes": "Modifica dettagli estensione", "requested-material-plant-changes": "Dettagli dell'estensione aggiornata", "no-data-available": "<PERSON><PERSON>un risultato disponibile", "action-types": {"additional-enrichment-request": "Richiesta di ulteriore arricchimento", "additional-edit-request": "Richiesta di modifica aggiuntiva", "approved": "A<PERSON>rovato", "cancelled": "Cancellato", "completed": "Completato", "done": "<PERSON><PERSON>", "draft": "<PERSON><PERSON>", "erp-updated": "ERP aggiornato", "feedback-received": "Feedback ricevuto", "in-progress": "In Corso", "integration-errors": "Errore di integrazione", "in-your-queue": "In coda", "rejected": "Rifiutato", "requires-more-information": "Ulteriori informazioni richieste", "submitted": "Inviato", "waiting-for-edit": "In attesa di modifica", "waiting-for-external-system": "In attesa del sistema esterno", "waiting-for-manual-handling": "In attesa di gestione manuale", "waiting-for-starting": "In attesa dell'avvio", "creation": "Creazione", "enrichment": "<PERSON><PERSON><PERSON><PERSON>", "fix-entity": "<PERSON>i <PERSON>", "relationship": "Relazione", "duplicates": "<PERSON><PERSON><PERSON><PERSON>", "extension": "Estensione Divisioni", "waiting-for-handling": "In attesa di gestione", "waiting-for-feedback": "In attesa di un riscontro", "erp-error": "Errore ERP", "waiting-for-enrichment": "In attesa di arricchimento", "waiting-for-approval": "In attesa di approvazione", "waiting-for-erp": "In attesa dell'ERP", "waiting-for-erp-create": "In attesa di creazione lato ERP", "waiting-for-erp-update": "In attesa di aggiornamento lato ERP", "waiting-for-job-done": "In attesa che il lavoro venga completato", "waiting-for-apply": "In attesa", "more-information-needed": "Altre informazioni necessarie"}, "approved-by": "A<PERSON><PERSON><PERSON> da", "assigned-to": "Assegnato a", "completed-by": "Completato da", "created-by": "<PERSON><PERSON><PERSON>"}, "status-page": {"about": {"title": "Informazioni sul sito", "subtitle": "Qui pubblicheremo aggiornamenti sulla disponibilità dei servizi TAM."}, "all-statuses-up": "<PERSON><PERSON> i sistemi sono operativi", "not-all-statuses-up": "Alcuni dei sistemi non sono operativi", "services": {"rabbitmq": "Amministrazione", "tam4_auth": "Autenticazione e gestione utenti", "tam4_data_loader": "Caricamento online dei dati", "tam4_duplicate_report": "Generazione report duplicati", "tam4_duplicates_management": "Gestione dei duplicati", "tam4_events_store": "Archivio eventi", "tam4_file_export": "Esporta file", "tam4_folder_navigation": "Esplora categorie", "tam4_legacy": "Contenuto legacy", "tam4_materials": "Mostra il contenuto", "tam4_notifications": "Notifica via e-mail", "tam4_ontology": "Capacità semantiche", "tam4_relationships": "Crea e gestisci le relazioni", "tam4_search": "Ricerca", "tam4_warnings": "Gestione avvisi", "tam4_workflow": "Workflow interno", "tam4_worklists": "Gestire le attività"}, "status": {"up": "Operativo", "down": "Non operativo"}}, "controls": {"notFoundText": "<PERSON><PERSON><PERSON> dato disponibile", "loading": "Caricamento..."}, "item-details": {"not-analyzed": "Non analizzato semanticamente", "confirm-activation-cta": "<PERSON><PERSON><PERSON>", "confirm-activation": "Vuoi confermare l'attivazione?", "no-data": "<PERSON><PERSON><PERSON> dato disponibile", "no-data-fields": "N<PERSON>un valore disponibile", "no-data-subtitle": "Mostra anche i valori vuoti", "relationship": {"type": "Tipo relazione", "description": "Test", "client": "<PERSON><PERSON><PERSON>", "code": "Materiale", "role": "<PERSON><PERSON><PERSON>", "no-data": "Nessuna relazione disponibile", "gr-instance": "Istanza del Golden Record"}, "instances": {"description": "Descrizione", "client": "Client", "code": "Codice Master Data", "no-data": "Nessuna istanza disponibile", "countries": {"title": "<PERSON><PERSON>", "empty": "N/A", "many": "{{data}} e altri {{other}} nazioni"}, "plants": {"title": "Plant", "empty": "N/A", "many": "{{data}} e altri {{other}} plants"}, "client-code": "Client/Codice", "action": "Operazione", "unlink": "<PERSON><PERSON><PERSON><PERSON> dal Golden Record"}, "fields": {"4_TAM_PlantPlanningInformation": "Pianificazione Impianto", "4_TAM_StorageLocations": "<PERSON><PERSON><PERSON><PERSON>", "4_TAM_PlantTotalValues": "Valori totali impianto", "4_TAM_MaterialValuation": "Valutazione materiale", "tables": {"plantKey": "Divisione", "reorderPoint": "Stock di riordino", "maximumStockLevel": "<PERSON><PERSON> di scorta", "minimumSafetyStock": "Stock di sicurezza", "minLotSize": "Dimensione minima lotto", "leadTimeInDays": "Tempo di consegna", "supportsMultipleValuations": "Riparabile?", "storageLocation": "Locazione dello stoccaggio", "client": "Client", "code": "Codice", "valuationClass": "Classe di Valutazione", "valuationCategory": "Categoria di Valutazione", "valuationType": "Tipo di Valutazione", "stockQuantity": "Quantità stock", "consumptionQuantity": "Quantità consumi", "orderedQuantity": "Quantità ordinata", "movingAveragePrice": "Prezzo medio mobile", "standardPrice": "Prezzo Standard", "priceUnit": "Unità Prezzo", "priceControlIndicator": "Indicatore di Controllo del prezzo", "totalStockQuantity": "Quantità in scorta Totale", "totalStockAmountEUR": "Valore Stock", "totalConsumptionAmountEUR": "<PERSON><PERSON> consumi (€)", "totalOrderedAmountEUR": "Ammontare Ordinato Totale", "movingAveragePriceEUR": "Prezzo media mobile (€)", "standardPriceEUR": "Prezzo Standard", "mrpGroup": "Gruppo MRP", "schedulingMarginKeyForFloats": "Chiave orizzonte per tempi buffer", "mrpController": "Responsabile MRP", "Purchasing_Group": "Gruppo di acquisto", "goodReceiptProcessingTimeInDays": "Tempo di processo in giorni del Golden Record ", "status": "Stato mat. valid. divisione", "safetyStock": "<PERSON>llo scorta di sicurezza", "safetyStockLevel": "<PERSON>llo scorta di sicurezza", "totalOrderedQuantity": "Quantità Ordinata Totale", "mrpType": "Tipo MRP", "lotSize": "Dimensione lotto", "lotSizeForProductCosting": "Dimensione del lotto per il calcolo del costo del prodotto", "serviceValuationClass": "Service Valuation Class", "stockAmount": "Valore Stock (valuta locale)", "consumptionAmount": "Valore consumi (valuta locale)", "orderedAmount": "Ammontare Ordinato Totale (valuta originale)", "purchasingGroup": "Gruppo d'acquisto", "currency": "Valuta del plant", "x": "x", "y": "y", "uma": "Unità di misura", "4_TAM_UnitOfMeasure": "UdM Standard", "roundingValueForPurchaseOrderQuantity": "", "unitOfIssue": "", "plantMaterialStatus": "", "validFromDate": "", "plantDeletionFlag": "", "seriable": "", "followUpMaterialCode": "", "logisticsHandlingGroup": "", "inHouseProductionTime": "", "individualColl": "", "controlKeyForQM": "", "certificateType": "", "batchManagementFlag": "", "fixedLotSize": "", "maximumLotSize": "", "orderingCosts": "", "storageCostsIndicator": "", "strategyGroup": "", "criticalPart": "", "effectiveOutDate": "", "countryOfOrigin": "", "loadingGroup": "", "planningTimeFence": "", "consumptionMode": "", "consumptionPeriodBackward": "", "consumptionPeriodForward": ""}, "materialId": "uuid", "materialKey": {"client": "Client", "materialCode": "Materiale"}, "materialGroup": "Gruppo merci", "materialType": "Tipo materiale", "manufacturerDetails": {"manufacturerCode": "Codice produttore", "manufacturerPartNumber": "Codice Materiale Produttore", "manufacturerName": "Nome produttore"}, "unitsOfMeasure": {"baseUnitOfMeasurement": "UM base", "purchasingUnitOfMeasurement": "Unità di misura di acquisto", "weightUnitOfMeasurement": "Unità di misura di Peso", "alternativeUnitsOfMeasure": "Unità di misura alternativa", "alternative": {"alternativeUnitOfMeasurement": "Unità di misura alternativa", "numerator": "Numeratore", "denominator": "Denominatore"}}, "basicData": {"deletionFlag": "Cancellazione materiale", "materialStatusValidFromDate": "Data inizio validità", "industrySector": "Settore industriale", "productDivision": "Settore merceologico", "oldMaterialNumber": "Vecchio codice materiale", "documentNumber": "Numero Documento", "basicMaterial": "Master Data Base", "laboratoryDesignOffice": "Ufficio di progettazione Laboratorio", "batchManagementRequirementIndicator": "Indicatore dei requisiti di gestione dei lotti", "authorizationGroup": "Gruppo autorizzativo", "crossPlantMaterialStatus": "Status anagrafica", "crossPlantPurchasingGroup": "Gruppo di acquisto Cross Plant", "genericItemGroup": "Gruppo Catategoria Articolo Generico", "externalMaterialGroup": "Gruppo materiali esterno", "hazardousMaterialNumber": "Material Number pericoloso"}, "dimensions": {"netWeight": "Peso netto", "grossWeight": "Peso <PERSON>", "sizeDimension": "Dimensione"}, "descriptions": {"shortDescriptions": "Testo breve", "purchaseOrderDescriptions": "Testo Esteso OdA", "internalNoteDescriptions": "Descrizione nodo interno", "longDescriptions": "Descrizione Lunga", "normalizedShortDescriptions": "Descrizione Corta Normalizzata", "normalizedLongDescriptions": "Descrizione Lunga Normalizzata"}, "metadata": {"revision": "Revisione", "lastUpdatedDate": "Data dell'ultimo aggiornamento", "createdDate": "Data di creazione", "semanticallyAnalyzed": "<PERSON><PERSON><PERSON><PERSON>"}, "completeness": "Completezza", "countries": "Countries", "plants": "Impiant<PERSON>", "attachments": "Allegati", "image": "<PERSON><PERSON><PERSON><PERSON>", "mdDomain": "Domain", "famiglia": "Famiglia", "sottoFamiglia": "<PERSON><PERSON>", "specificaTecnica": "Specifica Tecnica", "edizione": "Edizione", "revisione": "Revisione", "dataCustom": "Data", "productHierarchy": "Gerarchia prodotti", "volume": "Volume", "volumeUnit": "Unita di volume", "internationalArticleNumberEanUpc": "Numero Articolo Internazionale EAN UPC", "generic": "Masterdata Generico", "storageLocation": "Magazzino(i)", "priceUnit": "Prezzo unitario", "purchasingGroup": "Gruppo d'acquisto", "supportsMultipleValuations": "Consentire valutazioni multiple", "leadTimeInDays": "Tempi di consegna (giorni)", "checkingGroupAvailabilityCheck": "Verifica della disponibilità del gruppo", "controlCodeConsumptionTaxesForeignTrade": "Codice di controllo delle imposte sul consumo per il commercio estero", "genericMasterdata": "Masterdata Generico", "mrpGroup": "Gruppo MRP", "mrpType": "Tipo MRP", "mrpController": "Controller MRP", "logisticsGroup": "Gruppo Logistico", "reorderPoint": "Punto di riordino", "safetyStock": "Stock di sicurezza.", "minSafetyStock": "Stock di Sic. Min.", "maxStockLevel": "<PERSON><PERSON>", "standardPrice": "Prezzo Standard", "lotSize": "Dimensione Lotto", "minLotSize": "<PERSON><PERSON>. <PERSON><PERSON>", "multipleValuations": "Supporta valutazioni multiple", "currency": "Valuta", "masterdataStatus": "Stato Master Data", "originMaterial": "Materiale Originale", "intrastatCode": "Codice Intrastat", "cfopCategory": "Categoria CFOP materiale", "periodIndicator": "Indicatore dei decimali", "specialProcurementType": "Special Procurement Type", "valuationClass": "Valuation Class", "movingAveragePrice": "Media mobile prezzo", "priceControlIndicator": "Indicatore prezzo di controllo", "usageMaterial": "Utilizzo del materiale", "plantDataStatus": "Plant data Status", "profitCenter": "Centro di profitto", "plantOldMaterialNumber": "Plant Old Material Number", "lotSizeForProductCosting": "Costo produzione Lotto", "Min_Lot_Size": "Dimensione minima lotto", "Planned_Delivery_Time": "Tempo di consegna pianificato", "materialCFOPCategory": "Categoria CFOP", "status": "Plant Specific Material Status", "maximumStockLevel": "<PERSON><PERSON>", "minimumSafetyStock": "Stock di sicurezza minimo", "serviceValuationClass": "Service Valuation Class", "schedulingMarginKeyForFloats": "Chiave orizzonte per tempi buffer", "plantCurrency": "Valuta", "4_SDM_Currency": "Valuta", "storageBin": "Ubicazione di stoccaggio", "procurementType": "Special Procurement Type", "descriptionsTab": "", "roundingValueForPurchaseOrderQuantity": "", "unitOfIssue": "", "plantMaterialStatus": "", "validFromDate": "", "plantDeletionFlag": "", "seriable": "", "followUpMaterialCode": "", "logisticsHandlingGroup": "", "inHouseProductionTime": "", "individualColl": "", "goodReceiptProcessingTimeInDays": "", "controlKeyForQM": "", "certificateType": "", "batchManagementFlag": "", "fixedLotSize": "", "maximumLotSize": "", "orderingCosts": "", "storageCostsIndicator": "", "strategyGroup": "", "criticalPart": "", "effectiveOutDate": "", "countryOfOrigin": "", "loadingGroup": "", "planningTimeFence": "", "consumptionMode": "", "consumptionPeriodBackward": "", "consumptionPeriodForward": "", "valuationCategory": ""}, "completeness": "Completezza", "warnings": "Avvisi", "show-empty": "Mostra attributi vuoti", "hide-empty": "Nascondi attributi vuoti", "tabs": {"classifications": "Classificazioni", "relations": "Relazioni", "instances": "Istanze", "Tecnical Attributes": "Attributi tecnici", "customer-default": "Campi Cliente", "missing-tab": "Tab mancante"}, "semantically-analyzed-preview-popup": {"removed": "<PERSON><PERSON><PERSON>", "changed": "<PERSON><PERSON><PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON>", "field": "Campo", "before": "<PERSON><PERSON>", "after": "Prima", "action": "Azione", "no-fields-changed": "<PERSON><PERSON><PERSON> cambiamento sui campi", "semantic-analysis-preview": "Anteprima di analisi semantica"}, "errors": {"could-not-retrieve-attributes": "Non è stato possibile recuperare i dettagli degli attributi dell'oggetto dall'ontologia", "validators": {"alt-uom-int-value": "Inserire un valore numerico per il campo {{field}} alla riga: {{rowNumber}}", "alt-uom-max-length": "Il numero di cifre del campo {{field}} deve essere <= {{max_length}} per la riga: {{rowNumber}}"}}, "image": {"remove-image": "<PERSON><PERSON><PERSON><PERSON> immagine", "upload": "Caricamento", "image-size-warning": "L'immagine deve avere dimensione minore di 5Mb", "image-format-warning": "L'immagine deve essere di tipo png, jpeg o bmp"}, "confirm-activation-text": "L'attivazione includerà questo Materiale nell'ambito dell'IA, migliorandone la categorizzazione e gli attributi. L'attivazione conta come creazione secondo il vostro modello di licenza?", "user-material-plant-mismatch": "Non sei autorizzato a lavorare su nessuno degli stabilimenti del materiale.", "edit": "Modifica", "external-link": {"title": "Avviso: Stai uscendo da TAM!", "body": "{{external-url}} non è un sito ufficiale di Creactives, si prega di verificare se il dominio <b>{{external-domain}}</b> è affidabile. Non inserire mai le tue credenziali su un sito web non attendibile.", "accept-label": "Considera attendibile il dominio e apri", "reject-label": "<PERSON><PERSON><PERSON>"}}, "tabular-reports": {"email-sent": "Email inviatta"}, "warning": {"title": "Warning"}, "task-notes-history-popup": {"modal-title": "Cronologia delle note attività", "date": "Data", "user-id": "Utente", "comment": "Commento", "role": "<PERSON><PERSON><PERSON>"}, "link-unlink": {"golden-record-response": {"instances-have-ongoing-link-unlink-process": "", "instances-have-ongoing-enrichment-process": "", "gr-has-ongoing-enrichment-process": "", "invalid-clients": "ci sono già istanze con lo stesso cliente", "pending-processes": "Ci sono ancora processi in corso per il goldend record o le istanze selezionate"}}}, "login": {"signin-header": "Accedi alla Gestione degli Attributi Tecnici", "reset-password-header": "Reimposta la tua password", "field-required": "Questo campo è richiesto.", "password-format-incorrect": "La password non può iniziare o finire con uno spazio", "signin": "Accedi", "saml-signin": "Accesso singolo con SAML", "saml-not-authorized": "Utente non autorizzato ad accedere tramite SAML2.0", "reset": "Invia", "reset-password-needed": "È necessario impostare una nuova password personale. Do<PERSON> aver cliccato su Invia, sarà necessario completare il login utilizzando la nuova password.", "error-while-contacting-auth-server": "Si è verificato un errore contattando il server di autenticazione.", "forgot-password": "Password dimenticata?", "request-reset-password": {"header": "Password dimenticata?", "description": "Inser<PERSON>ci il tuo indirizzo e-mail per reimpostare la tua password:", "email-not-correct": "Inserire un indirizzo e-mail valido", "cancel": "Can<PERSON><PERSON>"}, "email-submitted-successfully": "È stato inviato con successo al tuo indirizzo e-mail un messaggio per resettare la password.", "reset-token-expired": "La richiesta per resettare la password è scaduta.", "reset-failed": "Qualcosa è andato storto! L'operazione non può essere completata.", "reset-success": "La tua password è stata reimpostata con successo.", "product-name": "Technical Attribute Management", "unable-to-login": "Errore durante il login. L'utente non esiste, la password non è corretta o l'account è scaduto!", "user-locked": "L'utente è stato bloccato!", "invalid-password": "La password deve rispettare le seguenti indicazioni:\n* lunghezza compresa tra 16 e 64 caratteri\n* avere almeno una lettera minuscola\n* avere almeno una lettera maiuscola\n* avere almeno un numero\n* avere almeno un carattere speciale\n* avere meno di 2 caratteri consecutivi ripetuti (es. 11, aa, **)\n* avere meno di 4 caratteri consecutivi (es. abc, 123)\n* diversa dalla login\n\nNota: i caratteri speciali ammessi sono i seguenti (il primo è uno spazio bianco) \n␣ * . ! @ # $ % ^ & ( ) { } [ ] : ; < > , . ? / ~ _ + - = | \\", "waiting-sso-token": ""}, "pages": {"page-not-found": "Pagina non trovata", "page-forbidden": "Accesso negato", "reset-password": {"not-equal": "Nuova password e conferma password non sono uguali!", "invalid-password": "La tua password non rispetta le regole di complessità 8-64 caratteri, almeno 1 maiuscola, almeno 1 minuscola, almeno 1 numero, almeno 1 carattere speciale, no username, ripetizioni o sequenze di caratteri, password comuni o contestuali", "invalid-password-new": "La tua password non rispetta le regole di complessità 8-64 caratteri, almeno 1 maiuscola, almeno 1 minuscola, almeno 1 numero, almeno 1 carattere speciale, no username, ripetizioni o sequenze di caratteri, password comuni o contestuali"}, "profile": {"title": "Il tuo profilo", "email": "Email", "company": "Azienda", "personal-information": "Informazioni personali", "change-password": "Cambia la password", "security-and-login": "Sicurezza & login", "email-settings": "Impostazioni email", "fallback-languages": "Lingue della descrizione secondaria", "personal-information-page": {"small-title": "Visualizza i miei dati personali", "first-name": "Nome", "last-name": "Cognome", "email": "Email", "company": "Azienda"}, "change-password-page": {"small-title": "cambia la password del tuo account", "not-equal-password": "Nuova password e conferma password non sono uguali!", "password-changed": "Password cambiata con successo", "new-password": "Nuova password", "confirm-password": "Conferma password", "cto-submit": "Cambia Password", "cto-cancel": "<PERSON><PERSON><PERSON>", "old-password": "Password attuale", "invalid-password": "La password deve rispettare le seguenti indicazioni:\n* lunghezza compresa tra 16 e 64 caratteri\n* avere almeno una lettera minuscola\n* avere almeno una lettera maiuscola\n* avere almeno un numero\n* avere almeno un carattere speciale\n* avere meno di 2 caratteri consecutivi ripetuti (es. 11, aa, **)\n* avere meno di 4 caratteri consecutivi (es. abc, 123)\n* diversa dalla login\n\nNota: i caratteri speciali ammessi sono i seguenti (il primo è uno spazio bianco) \n␣ * . ! @ # $ % ^ & ( ) { } [ ] : ; < > , . ? / ~ _ + - = | \\"}, "email-settings-page": {"small-title": "<PERSON><PERSON><PERSON> le tue preferenze email", "no-email-settings": "Nessuna impostazione email caricata", "email-changed": "Email modificata correttamente", "ignore-creation": "Ignora email di creazione", "ignore-approver": "Ignora email di approvazione", "ignore-edit": "Ignora email di modifica", "ignore-enrichment": "Ignora email di modifica", "ignore-classification-hint": "Ignora le email suggerimenti per la classificazione", "ignore-bulk-upload": "Ignora le email di caricamento in blocco", "cto-submit": "Invia", "cto-cancel": "<PERSON><PERSON><PERSON>"}, "fallback-languages-page": {"small-title": "Cambia la lingua di descrizione secondaria", "tooltip": "Le lingue secondarie intervengono solo in assenza della lingua impostata dall'utente per l'applicazione (lingua UI) nella descrizione materiale.", "fallback-languages": "<PERSON>ue secondarie", "submit": "Aggiornamento"}}}, "global-filters": {"domain": {"default-title": "<PERSON>inio", "materials-title": "Materiali", "services-title": "<PERSON><PERSON><PERSON>", "materials-golden-record-title": "Materiale Record d'Oro", "services-golden-record-title": "<PERSON><PERSON><PERSON>"}, "filter-heading": "Filtri geografici", "filter-operator": "è", "category": "Categoria", "plant": "Divisione", "country": "<PERSON><PERSON>", "client": "<PERSON><PERSON><PERSON>", "preview": {"cancel": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON> tutto", "apply": "Applica", "active-filters": "<PERSON><PERSON><PERSON> attivi", "set-default-filter-successfully": "Filtro predefinito impostato successo", "unset-default-filter-successfully": "Filtro predefinito rimosso con successo", "save-filters": "<PERSON><PERSON> filtri", "change-name-successfully": "Il nome è stato cambiato con successo", "share-filter-successfully": "Filtro condiviso successo", "unshare-filter-successfully": "Filtro impostato su privato con successo", "saved-filters": {"tab-title": "<PERSON><PERSON><PERSON> salvati", "shared-with-me": "Condiviso con me", "private-only": "<PERSON><PERSON><PERSON> da me", "no-filters-found": "Nessun filtro salvato trovato!", "filter": {"delete": "Can<PERSON><PERSON>", "apply-filters": "Applica filtri", "public": "Pubblico", "private": "Privato", "plant": "Divisione", "plants-text": "=0 {La divisione non è stata specificata} =1 {plant} >1 {plants}", "plants-details": "=1 {è {{plantsList}}} =2 {sono {{plantsList}}} >2 {sono {{plantsList}} o altre {{plantsRemainingCount}} divisioni}", "country": "<PERSON><PERSON>", "countries-text": "=0 {Il paese non è stato specificato} =1 {country} >1 {country}", "countries-details": "=1 {è {{countriesList}}} =2 {sono {{countriesList}}} >2 {sono {{countriesList}} o altre {{countriesRemainingCount}} paesi}", "client": "<PERSON><PERSON><PERSON>", "clients-text": "=0 {Il mandante non è stato specificato} =1 {client} >1 {clients}", "clients-details": "=1 {è {{clientsList}}} =2 {sono {{clientsList}}} >2 {sono {{clientsList}} o altri {{clientsRemainingCount}} mandanti}", "category": "Categoria", "taxonomy-name": "{{taxName}}", "materials-classified": "Materiali sono classificati in", "category-not-specified": "La categoria non è stata specificata", "tooltips": {"remove-as-default": "<PERSON><PERSON><PERSON> per rimuovere come predefinito", "set-as-default": "Imposta come predefinita"}}}}, "popup": {"title": {"category": "Sfoglia categoria", "plant": "Sfoglia divisioni", "country": "Sfoglia paesi", "client": "Sfoglia client"}, "no-data-found": "Non ci sono risultati che soddisfano la tua richiesta"}, "favourite-tab": {"text": "<PERSON><PERSON><PERSON> attributo in questa sezione <br> Usa Cerca per aggiungere categorie qui"}, "save-filters": {"title": "Salva i filtri correnti", "save": "<PERSON><PERSON>", "type-name": "Digita un nome", "mark-as-default": "Segna come predefinito", "default-tooltip": "Seleziona filtri impostati come default", "filter-section": {"is": "è", "are": "sono", "plant": "Divisione", "plants": "Divisioni", "country": "<PERSON><PERSON>", "countries": "<PERSON><PERSON>", "client": "<PERSON><PERSON><PERSON>", "clients": "<PERSON><PERSON><PERSON>", "category": "Categoria", "taxonomy-name": "{{taxName}}", "missing-plant-filter": "L'impianto non è stato specificato", "missing-country-filter": "Il paese non è stato specificato", "missing-client-filter": "Il mandante non è stato specificato", "missing-categories-filter": "Materiale e servizi non sono stati specificati", "or-others": " o altri {{count}} "}, "empty-state": {"title": "<PERSON><PERSON><PERSON> filtro impostato", "subtitle": "È possibile salvare i filtri correnti dopo aver scelto i valori per uno o più filtri."}, "save-successfully": "Filtri globali salvati correttamente", "error": {"name-required": "Fornisci un nome per i tuoi filtri"}, "delete-save-filters": {"delete-successfully": "Filtri salvati correttamente"}}}, "ab-inbev-erp-integration": {"errors": {"multiple-upload-error": "Impossibile caricare file multipli", "empty-requests-for-excel-creation": "La creazione del file Excel non può essere eseguita perché non sono state generate nuove richieste dall'ultimo Excel."}}, "relationships": {"errors": {"material-already-secondary-in-other-relationship": "Il materiale selezionato è gia secondario in un'altra relazione.", "secondary-material-is-already-primary-in-this-relationship": "Il materiale selezionato è già primario in un'altra relazione.", "material-is-already-in-another-relationship-delete-process": "Il materiale selezionato è già in un processo di cancellazione.", "duplicated-materials-found-in-requested-relationship": "Nella relazione richiesta sono stati trovati materiali duplicati", "interchangeable-relationship-contains-more-than-two-materials": "La relazione contiene più di due materiali", "error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Qualcosa è andato storto, per favore riprova più tardi", "primary-is-missing": "Mandante {{client}}: selezionare un materiale primario", "multiple-primaries-with-same-client": "Mandante {{client}}: multipli materiali selezionati come primari", "secondary-is-missing": "Mandante {{client}}: selezionare almeno un materiale secondario. Una relazione necessita di almeno due materiali.", "validation-failed": "Validazione fallita", "material-is-already-in-another-relationship-process": "Il materiale selezionato è già in un processo di creazione di una relazione.", "material-is-already-in-another-relationship": "Il materiale selezionato è già in una relazione.", "relationship-must-contains-at-least-two-materials": "Mandante {{client}}: selezionare almeno 2 materiali.", "substitute-or-substituted-is-missing": "Client {{client}}: selezionare un item che può essere sostituito ed uno che può sostituire", "relationship-must-contains-at-least-two-materials-no-client": "Selezionare almeno 2 materiali.", "substitute-or-substituted-is-missing-no-client": "Selezionare un item che può essere sostituito ed uno che può sostituire"}, "statistics": {"indicators": {"duplicate-count": "Relazioni di duplicazione", "equivalence-count": "Relazioni di equivalenza", "interchangeable-count": "Relazioni di intercambiabilità", "norelationship-count": "Relazioni di No-relationship", "total-count": "Relazioni create"}, "filters": {"title": "Esplora i tuoi dati", "input-created-from": {"label": "Creato a partire da", "placeholder": "Scegli una data"}, "input-created-to": {"label": "<PERSON><PERSON><PERSON> fino al", "placeholder": "Scegli una data"}, "input-relationship-type": {"label": "Tipo di relazione", "placeholder": "Scegli un tipo di relazione"}, "applay-filters": "Applica filtri", "export-in-excel": "Esporta i dati grezzi in Excel", "calendar-config": {"apply": "Applica", "cancel": "<PERSON><PERSON><PERSON>", "clear": "Svuota"}, "export": "Esporta i dati grezzi in Excel"}, "relationship-by-types": {"title": "Relazioni per tipo", "page-empty": {"title": "Ancora nessuna relazione creata", "subtitle": "sotto<PERSON><PERSON><PERSON>"}}, "relationship-by-materials": {"title": "Num. di materiali coinvolti in relazioni", "page-empty": {"title": "Nessun tipo di relazione per materiali", "subtitle": "sotto<PERSON><PERSON><PERSON>"}}, "reporting-export-successful": "Il report è stato esportato con successo", "dashboard-empty-title": "La tua dashboard è vuota", "dashboard-empty-details": "Prova a cambiare i filtri o i filtri globali"}, "relationshipType": {"all-types": "<PERSON>tti i tipi", "total": "<PERSON>tti i tipi", "duplicate": "Duplicazione", "equivalence": "Equivalenza", "interchangeable": "Intercambiabilità", "norelationship": "No relationship"}, "search": {"no-results": {"title": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "subheading": "Non ci sono risultati che soddisfano la tua richiesta:", "suggestion-title": "Suggerimenti:", "line1": "Prova una combinazione di filtri differente", "line2": "Prova a ripristinare i filtri"}}}, "materials": {"errors": {"plants": {"plant-already-existing": "Divisione già esistente", "price-ok-currency-ko": "Manca valore valuta", "duplicate-plants": "Divisioni duplicate nella richiesta", "invalid-num-plants-to-update": "Numero di divisioni da aggiornare non valido", "plant-not-editable-because-missing": "Divisioni non modificabili perchè mancanti"}, "material-not-found": "Il materiale non è stato trovato!", "error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Qualcosa è andato storto, per favore riprova più tardi", "too-many-clients": "Impossibile eseguire l'operazione su un master data appartenente ad un client differente", "unauthorized-for-edit": "Utente non abilitato a modificare alcun Master Data selezionato", "bulk-extension": {"not-found": "File di estensione massiva con id {{id}} non trovato", "error-parsing-excel": "Errore di lettura del file excel", "get-plants-authorization": "Errore nel recupero delle autorizzazioni dei plants"}}, "indicators": {"material-count": "Materiali", "service-count": "<PERSON><PERSON><PERSON>", "total-stock-value": "Valore totale di stock", "total-consumption-value": "Valore totale consumato", "total-ordered-value": "Valore totale ordinato"}, "countries-by-stock": {"title": "Top 10 Paesi per stock (mln €)", "page-empty": {"title": "<PERSON><PERSON><PERSON> paese", "subtitle": "N<PERSON>un paese per stock"}}, "plants-by-stock": {"title": "Top 10 Divisioni per stock (mln €)", "page-empty": {"title": "Nessuna Divisione", "subtitle": "Nessuna divisione per stock"}}, "manufacturer-by-consumption": {"title": "Top 10 Produttori per consumo (mln €)", "page-empty": {"title": "<PERSON><PERSON><PERSON> produttore", "subtitle": "Nessun produttore per consumo"}}, "material-quality": {"title": "Qualità dei Materiali", "page-empty": {"title": "<PERSON><PERSON><PERSON> dato disponibile", "subtitle": "<PERSON><PERSON><PERSON> dato da visualizzare"}, "confidence-values": {"GOOD": "BUONO", "FAIR": "DISCRETO", "POOR": "SCARSO"}}, "categories-by-duplicate": {"title": "Top 10 Categorie per conteggio duplicati", "page-empty": {"title": "Non ci sono categorie con duplicati", "subtitle": "Non ci sono categorie con duplicati da visualizzare"}}, "search": {"title": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "subheading": "Non ci sono risultati corrispondenti ai tuoi filtri", "suggestion-title": "Suggerimenti:", "line1": "Prova una combinazione di filtri differente", "line2": "Prova a ripristinare i filtri"}, "dashboard-empty-title": "La tua dashboard è vuota", "dashboard-empty-details": "La tua dashboard è vuota. Qui puoi vedere le statistiche sui materiali", "service-quality": {"title": "Qualità dei servizi"}, "poor-item-quality": "Qualità media dell'articolo mediocre"}, "workflow": {"errors": {"cannot-start-process-plant-extension-already-requested": "Impossibile richiedere l'estensione della divisione, perché è già stata richiesta.", "error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Qualcosa è andato storto, per favore riprova più tardi", "cannot-start-process-materials-already-in-deduplication": "Impossibile richiedere la deduplica, perché è già stata richiesta.", "cannot-start-process-changes-overlap": "Impossibile avviare il processo. C'è un processo di modifica pendente sugli stessi campi per questo materiale.", "cannot-start-process-instances-overlap": "Non posso iniziare il processo. Esiste un processo di creazione GoldenRecord pendente sul una delle istanze selezionate: {{processCodes}}", "workflow-not-finished": "Il Workflow deve essere completato!", "relationship-must-contain-at-least-two-masterdata": "La relazione deve contenere almeno due masterdata.", "cannot-delete-gr-overlap": "", "cannot-delete-gr-instance-overlap": ""}, "no-result-view": {"title": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "subheading": "Non ci sono risultati che soddisfano la tua richiesta:", "suggestion-title": "Suggerimenti:", "line1": "Assicurati di aver impostato correttamente i filtri", "line2": "Prova una combinazione di filtri differente", "filter-combination-sugg": "Prova una combinazione di filtri differente", "reset-filters-sugg": "Prova a ripristinare i filtri"}, "result-view": {"item-list": {"material-card-flex": {"material-code": "Codice materiale", "countries": "<PERSON><PERSON>", "client": "<PERSON><PERSON><PERSON>", "category": "Categoria"}}}, "status-distribution": {"title": "Processi attivi: {{process-type}}", "page-empty": {"title": "<PERSON><PERSON><PERSON> dato", "subtitle": "Nessun processo attivo"}}, "process-statuses": {"creation": "Creazione", "enrichment": "<PERSON><PERSON><PERSON><PERSON>", "fix-entity": "<PERSON>i <PERSON>", "relationship": "Relazione", "duplicates": "<PERSON><PERSON><PERSON><PERSON>", "extension": "Estensione", "waiting-for-handling": "In attesa di presa in carico", "waiting-for-feedback": "In attesa di feedback", "erp-error": "Errore ERP", "waiting-for-enrichment": "In attesa di arricchimento", "waiting-for-approval": "In attesa di approvazione", "waiting-for-erp": "In attesa dell'ERP", "waiting-for-erp-create": "In attesa di creazione ERP", "waiting-for-erp-update": "In attesa di aggiornamento ERP", "waiting-for-job-done": "In attesa di completamento", "more-information-needed": "Altre informazioni necessarie", "request-apply": "Rrichiesta di finalizzazione", "waiting-for-apply": "In attesa di finalizzazione"}, "chart-top-categories": {"title": "Categorie principali per numero di processi", "page-empty": {"title": "Nessuna categoria", "subtitle": "Nessuna categoria per Processo"}}, "reporting": {"indicators": {"active-processes": "<PERSON>i attivi", "users": "Utenti coinvolti", "completed-enrichment-processes": "Arricchimenti Completati", "item-created": "Materiali Creati", "materials-created": "Materiali creati", "services-created": "<PERSON><PERSON><PERSON> crea<PERSON>"}, "countries-by-process": {"title": "Paesi principali per numero di processi", "page-empty": {"title": "<PERSON><PERSON>un paese per numero di processi", "subtitle": "Nessun paese per numero di processi. Qui saranno visibili tutti i paesi per numero di processi."}}, "plants-by-process": {"title": "Divisioni principali per numero di processi", "page-empty": {"title": "Nessuna divisione per numero di processi", "subtitle": "Nessuna divisione per numero di processi. Qui saranno visibili tutti le divisioni per numero di processi."}}, "dashboard-empty-title": "La tua dashboard è vuota", "dashboard-empty-details": "La tua dashboard è vuota. Qui saranno visibili le statistiche del workflow.", "active-processes-by-type": {"title": "Processi attivi per tipologia", "page-empty": {"title": "Nessun processo attivo", "subtitle": "Non ci sono processi attivi. Se hai filtri attivi, prova con una differente combinazione di filtri"}}}}, "errors": {"materials": {"material-with-this-id-not-found": "Il materiale non è stato trovato!", "file-type-not-allowed": "Il tipo di file selezionato non è ammesso", "invalid-file-extension": "Estensione vuota non ammessa!", "file-size-exceeded": "Il file supera i {{maxSize}}"}, "default": "Si è verificato un errore, riprovare più tardi", "save": "Un errore si è verificato durante il salvataggio, si prega di verificare i dati e riprovare.", "auth": {"sso-enabled": "Operazione non supportata, un SSO esterno è configurato per l'accesso"}}, "file-export": {"exports-requested": {"tab-name": {"today": "<PERSON><PERSON><PERSON>", "current-month": "<PERSON><PERSON>"}, "table-header": {"details": "<PERSON><PERSON><PERSON>", "status": "Status", "request-rate": "Data della richiesta", "export-rate": "Data dell'esportazione", "items-count": "Numero di materiali", "actions": "Azioni", "service": "<PERSON><PERSON> da"}, "item-type": {"materials": "Materiali", "duplicate-groups": "<PERSON><PERSON><PERSON><PERSON>", "relationships": "Relazioni", "tasks": "Task"}, "status": {"ready": "Pronto", "in-progress": "In corso", "in-queue": "In Coda", "error": "Errore", "canceled": "Cancellato"}, "item": {"request-date": "Data della richiesta", "export-date": "data dell'esportazione"}, "pagination": {"first": "vai alla prima pagina", "previous": "Indietro", "next": "avanti", "last": "vai all'ultima pagina"}, "empty": {"title": "Non ci sono file disponibili per il download", "subtitle": "I download che hai richiesto saranno disponibili qui"}}, "actions": {"download": "Download file", "delete": "Cancella file", "relaunch-file-processing": "Riavvia l'elaborazione del file", "abort": "<PERSON><PERSON><PERSON>", "autorefresh-toggle": "Auto-aggiorna", "reload": "Ricarica"}, "errors": {"error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Qualcosa è andato storto, per favore riprova più tardi"}}, "search": {"menu": {"edit": "Modifica", "request-edit": "<PERSON><PERSON> modifica", "show-details": "<PERSON><PERSON>", "plant-extension": "Richiedi estensione", "plant-edit": "Modifica dati relativi agli stabilimenti", "master-data-changelog": "Change log", "authorizations": {"action-not-enabled": "Azione non permessa", "user-material-plants-mismatch": "Non sei autorizzato a lavorare sulle divisioni attive per questo materiale", "authorization-info-not-available": "Non è stato possibile recuperare informazioni sull'autorizzazione", "material-flagged-for-deletion": "Questo materiale è marcato per eliminazione", "not-authorized": "Non autorizzato", "already-extended-on-all-user-plants": "Il materiale è già esteso su tutte le divisioni abilitate per il tuo account", "already-extended-on-all-plants": "Il Master Data è già esteso su tutti i plant abilitati", "material-plants-empty": "Master non abilitato su alcun plant", "cannot-edit-plant-for-services": "Impossibile modificare le informazioni sulle divisioni per i servizi", "no-user-material-plants-in-common": "Nessun impianto materiale utente in comune", "material-flagged-for-deletion-at-plant-level": "Materiale contrassegnato per l'eliminazione a livello di stabilimento", "no-relations-on-golden-record": "Non è possibile creare relazioni sul record d'oro", "missing-required-data": "<PERSON><PERSON> richiesti man<PERSON>ti", "master-need-to-be-created-first": "Il master deve essere creato prima.", "already-existing-edit-process": "Il processo di modifica in corso {{processCode}} richiesto da {{requester}} sta bloccando nuovi processi di modifica", "not-allowed-on-golden-record": "Azione non consentita sul Golden Record", "already-existing-relationship-deletion-process": "Il processo di cancellazione di relazione in corso {{processCode}} richiesto da {{requester}} sta bloccando nuovi processi di modifica"}, "additional-actions": "Altre azioni", "material-versioning": "Mostra versionamento materiale", "edit-golden-record": "Modifica Golden Record", "copy-material": "Copia materiale", "link-instances": {"popup": {"title": "Collega istanze ad un Golden Record", "error": {"client": "Il client {{clientId}} è già stato selezionato per il collegamento!", "golden-record": "Un Golden Record è già stato selezionato", "instances": "L'istanza selezionata è già stata associata ad un Golden Record", "invalid-clients": "Il client {{clientId}} è già usato da un'altra istanza del Golden Record"}, "validate": {"golden-record": "Selezionare un Golden Record per procedere!", "instances": "Almeno un'istanza deve essere selezionata per procedere!", "deletion": "L'istanza selezionata risulta cancellata!"}}}, "delete-golden-record": "Elimina Golden Record"}, "loading-view": {"searching": "Ricerca in corso..."}, "no-result-view": {"title": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "subheading": "Non ci sono risultati che soddisfano la tua richiesta:", "suggestion-title": "Suggerimenti:", "line1": "Assicurati di aver scritto le parole correttamente.", "line2": "Prova un'altra combinazione di filtri", "line3": "Prova ad utilizzare dei termini di ricerca diversi", "line4": "Prova ad utilizzare termini di ricerca più generici", "line5": "Prova ad utilizzare meno termini di ricerca."}, "result-view": {"item-list": {"material-card-flex": {"material-code": "Codice materiale", "countries": "<PERSON><PERSON>", "client": "<PERSON><PERSON><PERSON>", "category": "Categoria", "copy-material-code": {"copy": "Copia il codice materiale", "copied": "Copiato"}, "add-to-your-list": "Aggiungi alla tua lista", "delete": "Cancellato"}, "categories": {"head-text": "Categorie"}, "filters": {"head-text": "Attributi tecnici", "select-value": "(Seleziona un valore)", "submit": "Applica filtri", "value-not-exists": ""}}, "loading": "Caricamento"}, "search-input": {"placeholder": "<PERSON><PERSON><PERSON> qualcosa ..", "actions": {"advanced-search": "Ricerca avanzata", "manual-selection": "Selezione manuale", "export-materials": "Esporta anagrafiche", "export-selected-materials": "Seleziona i materiali da esportare", "request-edit": "Richiedi Modifica", "create-relationship": "Crea relazione", "add-to-your-list": {"title": "Aggiungi materiali alla tua lista", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "select-list": "Seleziona una lista"}, "remove-material-from-list": "<PERSON><PERSON><PERSON><PERSON> dalla lista", "perform-action": "Esegui Azione", "perform-actions-on-all-materials": "Esegui l'azione su tutti i materiali", "bulk-edit": "Richiedi modifica massiva", "bulk-extend": "Richiedi estensione massiva", "link-instances": ""}, "input-tooltip": "Non puoi aggiornare l'input di ricerca mentre sei in modalità di selezione !"}, "search-list": {"advanced-toggle": "Avanzate", "paging-title": "Visualizzazione corrente: {{itemsInPage}} risultati di {{resultCount}}", "item-selected": "{{selectedItems}} oggetto selezionato", "items-selected": "{{selectedItems}} og<PERSON><PERSON>", "no-items-selected": "Nessun articolo se<PERSON>zio<PERSON>o", "error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "file-export": {"launch-file-export": "Esporta", "launch-file-export-successful": "Richiesta di esportazione generata", "export-selected-materials-successful": "Richiesta di esportazione generata a partire dai materiali selezionati"}}, "advanced-search": {"form-group-title": "<PERSON><PERSON><PERSON> aggiunti<PERSON>", "material-code": "Codice materiale", "created-between": "Materiali creati", "modified-between": "Materiali modificati", "material-group": "Gruppo materiale", "manufacturer": "Codice produttore", "manufacturer-part-number": "Codice Materiale Produttore", "old-material-number": "Vecchio codice materiale", "material-type": "Tipo Materiale", "actions": {"reset-filter": "Azzera filtro", "apply-filter": "Applica filtro"}, "placeholders": {"enter-value": "Inserisci valore", "start": "Vai", "end": "Fine"}, "cancel-popup": {"title": "<PERSON><PERSON><PERSON> filt<PERSON> a<PERSON>", "content": "La chiusura del pannello filtri avanzato rimuoverà tutti i filtri attivi.", "question": "Vuoi procedere?", "actions": {"confirm": "Si", "cancel": "No"}}, "status": "Stato dell'anagrafica", "obsolete": "Includi stati obsoleti", "tooltip": "Puoi cercare più materiali usando lo spazio: e.s., CODE1 CODE2 CODE3", "manufacturer-name": "Nome produttore", "gr-instances": "Mostra le istanze dei golden record", "options": "Opzioni di ricerca", "deleted": "Include anagrafiche cancellate", "gr-only": "Mostra solo i golden record", "statusTooltip": "Si prega di selezionare un client nei filtri globali per ottenere lo stato dei Master Data", "completeness": "Completezza", "customer-fields": "<PERSON><PERSON>"}, "warnings": {"warning-types": "Tipi di Avviso", "warning-type": {"goods-receipt-processing-time": "Tempo di processo GR non è compilato", "plant-specific-material-status": "Lo stato materiale spec. della Divisione non è compilato", "completeness": "Materiali con scarso livello di <PERSON>zza", "material-group": "Gruppo merce non presente o non valido"}, "too-many-records": "Il numero totale di risultati è {{resultCount}}, verranno esportati solo i primi 10.000."}, "relationship": {"parent-material": "Vai al materiale primario", "gr-instance": "Istanza di "}, "new-list-popup": {"title": "Nuova lista", "actions": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>"}, "form": {"list-name": "Nome Lista", "list-name-length": "Massimo 30 caratteri.", "list-name-chars-left": "{{value}} <PERSON><PERSON><PERSON>.", "errors": {"max-length": "Il nome della lista può avere al massimo 30 caratteri", "list-name-required": "Il nome della lista è obbligatorio"}}}, "list-dropdown-menu": {"standard-search": "Ricerca standard", "no-lists-available": "Nessuna lista disponibile", "create-new": "Crea nuovo", "import": "Importa", "delete": {"confirm-title": "Sei sicuro di voler eliminare questa lista?", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma"}, "active": "ATTIVO", "create-list-suggestion": "Prova a crearne una nuova con i materiali selezionati", "tooltips": {"public-list": "Pubblica la lista", "private-list": "Rendi la lista privata", "delete-list": "Cancella lista"}, "lock-list": "La lista è ora privata", "share-list": "La lista ora è pubblica", "import-excel-popup": {"message1": "Please upload a file in which the first two columns represents client and master data code", "message2": "La prima riga è riservata all'intestazione, in prima colonna il client, in seconda il codice materiale. Il sistema leggerà i dati dalla seconda riga.", "message3": "Eventuali colonne addizionali verranno ignorate.", "message4": "Trascinate il file nel riquadro sottostante o, semplicemente, fate clic su di esso per avviare il caricamento.", "drag-and-drop": "Trascinate il file qui", "or": "or", "browse": "Browse for file"}}, "ignored-materials-popup": {"partial-import": {"title": "Lista creata parzialmente", "message": {"title": "La lista è parzialmente creata.", "content-one-material": "1 riga è stata ignorata.", "content": "{{count}} righe sono state ignorate."}}, "no-list-imported": {"title": "La lista non può essere importata!", "message": "Tutti i materiali sono stati ignorati. Assicurati che i codici siano corretti o prova ad importare una nuova lista."}, "import-success": {"title": "La tua lista è stata importata con successo!", "message": "La ricerca verrà aggiornata."}, "actions": {"close": "<PERSON><PERSON>", "view-list": "Vedi lista", "export-ignored": "Esporta ignorati"}}, "operations": {"completed": "Operazione completata con successo"}, "errors": {"default": "Qualcosa è andato storto, per favore riprova più tardi", "reached-max-list-limit": "Hai raggiunto il limite di liste attive (25). Si prega di archiviare alcune liste esistenti per crearne di nuove.", "multiple-upload-error": "Non è possibile caricare file multipli"}, "pagination": {"first": "Primo", "previous": "Precedente", "next": "<PERSON><PERSON>", "last": "Ultimo"}, "bulk-edit-popup": {"title": "Modifica Massiva", "operation-details": {"title": "Dettagli operazione", "step": "Passaggio 1 di 2", "subtitle": "Seleziona l'azione(i) massiva che vuoi eseguire sui {{selectedItems}} Master Data Items", "change": "Modifica ", "apply-to-empty": "Applica solo ai valori vuoti", "insert-value": "Inserisci valore", "choose-value": "Seleziona valore", "review-changes": "Controlla i cambiamenti", "editable-master-data": "Sei autorizzato a modificare solo {{editableItens}} all'interno dei {{selectedItems}} Master Data selezionati", "warning-no-tech-attributes": "Nessun campo disponibile per la modifica"}, "review-operations": {"title": "Controlla l'operazione", "step": "Passaggio 2 di 2", "updated-fields": "Aggiorna campi", "field-name": "Nome campo", "field-action": "Azione campo", "field-actions": {"change-to": "Modifica in", "fill-empty-with": "Riempi i valori vuoti con"}, "field-value": "Valore campo", "empty-value": "vuoto", "info-message": "Le descrizioni normalizzate saranno ricalcolate e  potenzialmente aggiornate per {{selectedItems}} materiali. Vuoi continuare?", "actions": {"back": "Indietro", "confirm-edit": "Conferma Modifica Massiva"}}, "confirm-success": "Modifiche massive confermate con successo", "operation-outcome": {"title": "Esito operazione", "actions": {"close": "<PERSON><PERSON>", "close-and-search": "Visualizza errori di ricerca", "close-and-search-info": "Questa operazione ripristinerà i filtri di ricerca", "back": "Indietro"}, "alert": {"success": "{{success}} di {{total}} correttamente avviati"}, "table": {"header": {"client": "Client", "code": "Codice", "outcome": "Esito", "outcome-description": "Descrizione Errore"}}}}, "form-inputs": {"validation-errors": {"required": "Il campo è richiesto", "maxlength": "Lunghezza massima superata", "valueRequiredWhenApplyOnlyToEmpty": "Il valore è richiesto quando \"applicare cambiamenti solo ai valori vuoti\" è presente"}}, "import-excel": {"title": "Importazione Excel"}, "bulk-extend-popup": {"title": "Si prega di selezionare la lista di plant su cui effettuare l'estensione"}, "export-modal": {"header": "Configurazione richiesta di estrazione", "labels": {"extractionType": "Scopo dell'estrazione", "exportTechnicalAttributes": "Esporta attributi tecnici", "lboxLanguages": "Lingue", "lboxDescriptions": "Descrizione", "tsField": "Selezione campi da esportare"}, "exportTypes": {"EXPORT_V2_CONSULTATION": "Consultazione", "EXPORT_V2_BULK_EDIT": "Modifica massiva"}, "btn": {"close": "<PERSON><PERSON>", "submit": "Lancia"}, "confirm": {"header": "Attenzione! ", "message": "L'operazione potrebbe richiedere qualche minuto per essere completata.\n Per monitorare l'esportazione accedere alla funzione \"Esportazione\" dal menu principale", "rejectBtn": "<PERSON><PERSON><PERSON>", "acceptBtn": "Conferma"}, "error": {"toManyItemsToExport": "Troppi item selezionati per l'esportazione: {{count}}/{{limit}} ", "mustChoseALanguage": "Occorre selezionare almeno una lingua", "mustChoseADescription": "Occorre selezionare almeno una descrizione", "bulkEditSingleClientOnly": "Impossibile procedere con l'estrazione per bulk-edit: solo masterdata dello stesso client possono essere selezionati."}}, "notification-export-request": {"limit-exceeded": "La richiesta supera il numero massimo di materiali esportabili!", "success": "Richiesta avvenuta con successo", "bulk-edit-single-client-only": "Impossibile procedere con l'estrazione per bulk-edit: solo masterdata dello stesso client possono essere selezionati."}}, "search-service": {"upload-details": {"errors": {"null-file": "Nessun file è stato selezionato. Si prega di scegliere un file .xlsx", "not-excel": "Il file selezionato non è valido. Si prega di scegliere un file .xlsx", "lines-number": "Il file contiene troppe righe, il limite è di 10k righe", "header-mismatch": "Il file deve avere 2 colonne", "missing-data": "Il file non ha dati"}}}, "warnings": {"home": {"warnings": "Avviso", "warning-section": {"title": "Panoramica avvisi per", "materials": "Materiali", "warning-overview": {"tite": "Panoramica avvisi", "view-period": {"all": "<PERSON><PERSON>", "week": "Set<PERSON><PERSON>", "month": "Mese"}}, "sort-by": "Ordina per", "count": "Numero", "name": "Nome", "services": "servizi"}}, "details": {"back-to-overview": "Ritorna alla panoramica", "details-for": "Dettagli avvisi per {{ section }} / {{warningType}}", "by-country": {"title": "<PERSON>"}, "by-plants": "Per Divisione", "by-category": "Per Categoria", "coverage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "categories": {"other": "<PERSON><PERSON>"}}, "warning-type": {"material-type": "Tipo Materiale", "material-group": "Gruppo materiali", "classification": "Classificazione"}, "page-empty": {"title": "Non ci sono avvisi", "subtitle": "Non sono stati generati avvisi in questa sezione"}, "errors": {"error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Qualcosa è andato storto, per favore riprova più tardi", "no-materials-matched-the-filter": "Nessun materiale trovato per la creazione della lista", "materials-number-exceeded-the-limit": "L'operazione non può essere eseguita su più di 10.000 materiali"}}, "worklists": {"messages": {"task-assigned-to-you": "L'attività ti è stata assegnata", "task-removed-assignment": "L'assegnazione dell'attività è stata rimossa", "fix-warnings": "Avviso risolto correttamente", "fix-warnings-ignored": "Warning ignorati", "set-resolution": "Risoluzione impostata correttamente", "currently-sorting-by": "Ordinamento corrente per", "all-tasks-assigned-to-you": "Tutte le attività sono state assegnate a te", "approved-all-bulk-changes": "Richiesta di approvazione della modifica massiva completata", "approved-selected-bulk-changes": "Le modifiche massive selezionate sono state approvate", "rejected-all-bulk-changes": "Richiesta di rifiuto di modifica massiva effettuata con successo", "rejected-selected-bulk-changes": "Queste modifiche massive selezionate verranno rifiutate", "successful-operation": "L'operazione è stata completata con successo"}, "errors": {"task-already-assigned": "L'attività era già stata assegnata a un altro utente", "page-not-found": {"title": "ELENCO DI ATTIVITÀ NON TROVATO", "subtitle": "CI DISPIACE, MA <PERSON>LENCO DI ATTIVITÀ CHE AVETE RICHIESTO NON È STATO TROVATO"}, "unknown-error": "Si è verificato un errore sconosciuto", "error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "default": "Qualcosa è andato storto, per favore riprova più tardi"}, "enrichment": {"empty": {"completed": {"title": "Nessun task completato recentemente", "subtitle": "Quando completi un task, comparirà qui."}, "in-queue": {"title": "<PERSON><PERSON><PERSON> task nella tua coda", "subtitle": "Quando un data manager assegna materiali a una delle tue divisioni, lo vedrai qui."}, "waiting-for-approval": {"title": "Nessun task in attesa di approvazione", "subtitle": "Quando completi uno dei task che ti sono stati assegnati, comparirà qui."}, "waiting-external-system": {"title": "Nessun task in attesa di ERP", "subtitle": "Quando uno dei tuoi task viene approvato, lo troverai qui in attesa di sincronizzazione con il sistema esterno."}, "waiting-feedback": {"title": "Non hai attività in attesa di feedback", "subtitle": "Quando chiederai ulteriori informazioni, appariranno qui"}}, "home": {"completed": {"title": "Task completati", "subtitle": "I tuoi task completati di recente."}, "in-queue": {"title": "Nella tua coda", "subtitle": "<PERSON>tti i materiali in attesa di arricchimento"}, "waiting-for-approval": {"title": "In attesa di approvazione", "subtitle": "I tuoi task in attesa di essere approvati."}, "waiting-external-system": {"title": "In attesa del sistema esterno", "subtitle": "I tuoi task in attesa di essere esportati."}, "waiting-feedback": {"title": "In attesa di feedback", "subtitle": "Le mie attività in attesa di feedback."}}, "assignment": {"last-updated-on": "Ultimo aggiornamento", "process": "Processo", "material-code": "Codice materiale", "assign-removal": "Rimuovi assegnamento", "assign-to-me": "Assegna a me", "creator": "<PERSON><PERSON><PERSON> <PERSON>", "assigned-to": "Assegnato a", "editor": "Editore", "approver": "Approvatore", "relationship-type": "Relationship Type", "requester-note": "Nota dell'richiedente", "approver-note": "Nota dell'approvatore", "approver-suggestion": "Suggerimento dell'approvatore", "approver-suggestion-before": "<PERSON><PERSON>", "approver-suggestion-after": "invece di creare un nuovo materiale", "last-update": "Ultimo aggiornamento", "countries": "<PERSON><PERSON>", "categories": "Categorie", "and-more": "e altri {{value}}", "materials-table": {"description": "Descrizione", "material-code": "Codice materiale", "status": "Stato ERP", "masterdata-status": "Stato del Master Data"}, "ignore": "Ignora", "mark-as-done": "Segna come completato", "rejection-cause": "Rifiutato, utilizza il seguente codice materiale", "material-not-available": "codice materiale non disponibile", "bulk-operation": "Operazioni Massive", "re-assign": "Riassegna", "assign-all-to-me": "Assegna tutti a me", "description-not-available": "Descrizione non disponibile", "ERP-note": "Nota ERP", "assign-success": "Master Data assegnati all'utente", "created-on": "Creato il", "note-history": "Mostra cronologia delle note..."}, "search": {"assigned-to-me": "Assegnato a me", "assigned-to-roles": "Assegnato a uno dei miei ruoli", "no-records-found": "Nessun record trovato.", "search-inputs": {"material-code": "Codice materiale:", "process-code": "Codice di processo:"}, "no-matching-results": "Non ci sono risultati corrispondenti alla ricerca.", "no-matching-global-filters-results": "Non ci sono risultati corrispondente alla vostra combinazione di filtri corrente.", "suggestions": "<PERSON><PERSON><PERSON><PERSON>", "filter-combination-sugg": "Prova una combinazione di filtri diversa", "diff-search-terms-sugg": "Prova diversi termini di ricerca", "general-search-sugg": "Prova più termini di ricerca generali", "reset-filters-sugg": "Prova a ripristinare i filtri", "search-form": {"set-filters": "Imposta i filtri...", "material-code": {"label": "Filtra le attività per codice materiale", "placeholder": "e.g 50011768"}, "process-code": {"label": "Filtra le attività per codice di processo", "placeholder": "e.g P-201807-00027"}, "task-type": {"label": "Filtra attività per tipo", "placeholder": "(seleziona un tipo)"}, "warning-type": {"label": "Filtra task per tipo di warning", "placeholder": "(seleziona un tipo)"}, "sorting-type": {"label": "Ordina attività per data", "sort-asc": "Mostra prima i più recenti", "sort-desc": "Mostra prima l'ultimo recente"}, "clear-filters": "<PERSON><PERSON><PERSON> filtri", "bulk-operation-code": {"label": "Codice operazione massiva", "placeholder": "es. BE-202105-00103"}, "toggle-matching-materials-only": {"label": "Effetto dei Filtri Globali", "show-btn": "Mostra materiali non corrispondenti", "hide-btn": "Nascondi materiali che non corrispondono"}, "authorization-group": {"label": "Gruppo Autorizzativo"}, "material-group": {"label": "Material Group"}, "material-type": {"label": "Material Type"}, "completeness": {"label": "Completezza"}, "requester": {"label": "<PERSON><PERSON><PERSON>"}}}, "status": {"approved": "A<PERSON>rovato", "rejected": "Rifiutato", "more-information-needed": "Altre informazioni necessarie", "additional-enrichment-request": "Ulteriori richieste di arricchimento", "in-progress": "In corso", "erp-updated": "ERP aggiornato", "erp-error": "Errore ERP", "waiting-for-enrichment": "<PERSON><PERSON><PERSON><PERSON>'a<PERSON>", "enrichment-requested": "<PERSON><PERSON><PERSON><PERSON> richiesto", "waiting-for-approval": "In attesa di approvazione", "waiting-for-global-approval": "Aspettando l'approvazione globale", "waiting-for-country-approval": "In attesa dell'approvazione globale del paese", "waiting-for-erp": "In attesa dell'ERP", "waiting-to-be-processed": "In attesa di essere elaborato", "done": "<PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "draft": "<PERSON><PERSON>", "waiting-for-handling": "In attesa di gestione", "waiting-for-feedback": "In attesa di risposta", "waiting-for-starting": "In avvio", "aborted": "Abbandonato"}}, "duplicates": {"home": {"completed": {"title": "Task completati", "subtitle": "I tuoi task completati di recente."}, "in-queue": {"title": "Nella tua coda", "subtitle": "<PERSON>tti i materiali in attesa di arricchimento"}, "waiting-feedback": {"title": "In attesa di un riscontro", "subtitle": "I miei task in attesa di essere esportati"}}, "empty": {"completed": {"title": "<PERSON><PERSON><PERSON> attività è stata completata di recente", "subtitle": "Quando completerai delle attività, queste appariranno qui."}, "in-queue": {"title": "<PERSON><PERSON><PERSON> task nella tua coda", "subtitle": "Non hai attività in coda. Quando i data manager assegneranno elementi a una delle tue divisioni, li vedrai qui."}, "waiting-feedback": {"title": "Nessun task in attesa di feedback", "subtitle": "Quando chiederai ulteriori informazioni, appariranno qui"}}, "group": {"messages": {"no-group-found": "Nessun gruppo trovato da ispezionare", "confirmation-message": "Conferma per contrassegnare il lavoro come fatto", "job-done-successful": "Lavoro eseguito con successo"}, "actions": {"inspect-group": "Ispeziona gruppo", "display-in-separate-groups": "Visualizza in gruppi separati", "send-enrichment": "<PERSON><PERSON> modifica", "create-rel": "<PERSON><PERSON> come duplicati", "job-done": "Lavoro completato", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "display-single-page": "Visualizza pagina singola", "send-to-waiting": "Invia in attesa", "send-to-your-queue": "Aggiungi alla tua coda", "unassign": "Rimuovi assegnazione"}, "consumption-amount": "Valore totale consumato", "consumption-quantity": "Quantità totale consumata", "created-by": "<PERSON><PERSON><PERSON> <PERSON>", "assigned-to": "Assegnato a", "approver-note": "Nota approvatore", "requester-note": "Nota del richiedente", "last-update": "Ultimo aggiornamento il", "process": "# Processo: ", "affected-stock": "Valore di Stock coinvolto", "total-amount-stock": "Valore totale di stock a magazzino", "affected-quantity": "Quantità coinvolta", "total-amount": "Valore totale di {{value}}", "total-consumption": "Consumo totale", "total-consumption-value": "Valore totale consumato", "total-consumption-quantity": "Quantità totale consumata", "amount-consumed": "<PERSON><PERSON> consumato {{value}}", "confidence": "Confidenza", "classification": "Classificazione", "accuracy-level": "Livello di precisione", "of": "di", "metric-name": "Questo gruppo è stato creato confrontando nome e codice del produttore.", "basic-data": "Dati di base", "material-type": "Tipo Materiale", "technical-attributes": "Attributi tecnici", "fields-without-values": "Campi senza valori", "description": "Descrizione", "unit-of-measure": "Unità di misura", "metric": {"Code": "Questo gruppo è stato creato confrontando nome e codice del produttore.", "TechnicalSheets": "Questo gruppo è stato creato confrontando i valori delle schede tecniche."}, "groups-all": {"card-filter-viewed": "Stai visualizzando {{groupLength}} gruppi su {{totalGroups}} disponibili"}, "groups-exact-match": {"card-filter-viewed": "Stai visualizzando {{groupLength}} gruppi su {{totalGroups}} disponibili"}, "groups-hidden": {"card-filter-viewed": "Stai visualizzando {{groupLength}} gruppi su {{totalGroups}} disponibili"}, "financial-data": "Valutazioni", "affected-ordered": "Ordinato coinvolto", "display-valuations": "<PERSON>ra valori", "assigne-group": "Assegna gruppo", "total-ordered": "Ordini di acquisto totali", "total-ordered-quantity": "Quantità totale ordini di acquisto"}, "table": {"material-code": "Codice materiale", "description": "Descrizione"}, "confirm-popup": {"actions": {"send-to-waiting": {"title": "Invia alla coda in attesa", "message": "Sei sicuro di voler spostare questo gruppo nella coda in attesa?"}, "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma"}}}, "approver": {"extension-approval-popup": {"title": "Approva estensione divisioni", "approval-note": "Scrivi il motivo della tua decisione", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "reject": "<PERSON><PERSON><PERSON><PERSON>"}, "relationship-approval-popup": {"title": "Approva relazione", "approval-note": "Scrivi il motivo della tua decisione", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "reject": "<PERSON><PERSON><PERSON><PERSON>"}, "plant-update-approval-popup": {"title": "Approva aggiornamento divisioni", "approval-note": "Scrivi il motivo della tua decisione", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "reject": "<PERSON><PERSON><PERSON><PERSON>"}, "reject-title": "<PERSON><PERSON><PERSON><PERSON> rich<PERSON>a", "request-more-info": "Richiesta di ulteriori informazioni"}, "filter": {"currentView": "Visualizzazione corrente: {{visibleItemsCount}} attività di {{totalItemsCount}}"}, "pagination": {"first": "Primo", "previous": "Precedente", "next": "<PERSON><PERSON>", "last": "L`ultimo"}, "task-type": {"extension_creation": "Estensione", "extension_update": "Aggiornamento estensione", "enrichment": "Modifica", "creation": "Creazione", "relationship_creation": "Relazioni", "duplicates": "<PERSON><PERSON><PERSON><PERSON>", "fix_entity": "Warning", "gr_creation": "Creazione Golden Record", "gr_enrichment": "Modifica Golden Record", "gr_scratch_creation": "Creazione Golden Record da zero", "gr_deletion": "Eliminazione Golden Record"}, "resolve-warning-popup": {"title": "Aggiusti gruppo di materiale", "material-code": "Codice materiale", "plant": "Divisione", "status": "Stato", "mmsta": "Stato materiale specifico della divisione", "webaz": "Tempo di elaborazione entrata merci in giorni", "hazardous-material": "Material Number pericoloso", "individual-coll": "Requisiti individuali e collettivi", "margin-key": "Chiave orizzonte per tempi buffer", "part-weight": "Peso lordo", "plant-specific-gr-time": "Tempo di elaborazione del ricevimento merci specifico per il plant", "plant-specific-material-status": "Stato del materiale specifico del plant", "minimum-ordering-quantity": "Quantità minima ordinabile", "mrp-controller-definitions": "Selezionare il nuovo valore", "purchasing-group-definitions": "Seleziona il nuovo valore", "mrp-controller-definitions-previous": "<PERSON><PERSON> corrente", "purchasing-group-definitions-previous": "<PERSON><PERSON>", "no-values": "(Seleziona un valore)", "serial-no-profile": "Numero seriale del profilo", "size-dimension": "Dimensione", "warranty": "Garanzia", "individual-and-collective": "Requisiti individuali e collettivi", "individual": "Requisiti esclusivamente individuali", "collective": "Requisiti esclusivamente collettivi", "in-house-production-and-delivery-time": "Produzione in casa e tempo di consegna", "description": "Descrizione", "manufacturer": "Informazioni sul produttore", "current-material-group": "Gruppo materiale corrente", "suggested-material-groups": "Gruppi di materiali suggeriti", "manual-search": {"try-searching-manually": "Nessuna di queste è corretta? Prova a cercare manualmente", "here": "qui"}, "actions": {"cancel": "<PERSON><PERSON>", "confirm": "Conferma la selezione", "ignore-suggestion": "Ignora i suggerimenti, mantieni il tuo valore attuale"}}, "mrp-controller-definitions-popup": {"mrp-controller-definitions": "Definizioni controllo MRP", "mrp-controller-definitions-previous": "<PERSON><PERSON> at<PERSON>"}, "purchasing-group-definitions-popup": {"purchasing-group-definitions": "Definizioni gruppo d'acquisto", "purchasing-group-definitions-previous": "<PERSON><PERSON> at<PERSON>"}, "individual-coll-warning-popup": {"individual-and-collective": "Requisiti individuali e collettivi", "individual": "Solo requisiti individuali", "collective": "Solo requisiti collettivi"}, "quick-fix-status-popup": {"title": "Correggi lo stato della divisione per il materiale"}, "quick-fix-gr-time-popup": {"title": "Correggi il tempo di elaborazione della ricevuta merci"}, "scheduling-margin-key-definitions-popup": {"margin-key": "Chiave orizzonte per tempi buffer"}, "reassign": {"title": "Riassegna", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "no-roles": "<PERSON><PERSON><PERSON> ruolo trovato"}, "inspect": {"more-information-needed": "Modifica", "additional-enrichment-request": "Modifiche aggiuntive richieste", "erp-error": "Controlla gli <PERSON>i", "waiting-for-enrichment": "Modifica", "waiting-for-approval": "<PERSON><PERSON><PERSON><PERSON>", "default": "Ispeziona", "approved": "A<PERSON>rovato"}, "relationships": {"DUPLICATE": "DUPLICAZIONE", "EQUIVALENCE": "EQUIVALENZA", "INTERCHANGEABILITY": "INTERCAMBIABILITÀ", "INTERCHANGEABLE": "INTERCAMBIABILI", "NORELATIONSHIP": ""}, "bulk-popup": {"bulk-approve": "Approvazione Massiva", "title": "Appova modifica massiva", "approving-changes": "Stai approvando i cambiamenti su {{materialsNumber}} materiali:", "field": "Campo", "action": "Azione", "value": "Valore", "material-details": "Dettagli Materiale", "material-code": "Codice Materiale", "material-description": "Descrizione", "write-reason": "Sc<PERSON>vi il motivo della tua decisione:", "reject": "<PERSON><PERSON><PERSON><PERSON> tutto", "reject-selected": "Rifiuta {{selectedCount}} selezionati", "confirm": "<PERSON><PERSON><PERSON><PERSON> tutto", "confirm-selected": "App<PERSON>a selezionati ({{selectedCount}})", "cancel": "<PERSON><PERSON><PERSON>", "field-actions": {"change-to": "Cambia in", "fill-empty-with": "Riempi i valori vuoti con"}, "title-extension_creation": "Approvazione Creazione Estensione Massiva", "title-extension_update": "Approvazione Aggiornamento Estensione Massiva", "title-enrichment": "Approvazione Aggiornamento Massivo", "info-bulk-extension-creation": "Il {{uploadDate}}, {{uploadedBy}} ha caricato il file {{fileName}} contenente un totale di {{numberOfExtensions}} extensioni a nuovi plant riguardanti {{distinctMasterdataCount}} masterdata e {{distinctPlantCount}} plant.", "info-bulk-extension-update": "Il {{uploadDate}}, {{uploadedBy}} ha caricato il file {{fileName}} contenente un totale di {{numberOfExtensionUpdates}} estensioni aggiornate riguardanti {{distinctMasterdataCount}} masterdata e {{distinctPlantCount}} plant.", "info-bulk-extension-enrichment": "Il {{uploadDate}}, {{uploadedBy}} ha caricato il file {{fileName}} contenente un totale di {{updatedDescriptions}} descrizioni aggiornate per {{distinctMasterdataCount}} masterdata, riguardanti {{distinctLanguages}} lingue.", "title-creation": "Approva creazione massiva", "info-bulk-creation-summary": "Il {{uploadDate}}, {{uploadedBy}} ha caricato il file {{fileName}} con {{distinctMasterdataCount}} nuove creazioni di masterdata relative a {{distinctPlantCount}} plant: ", "popup-confirm-title": "Conferma", "popup-confirm-message": "Alcuni dei Masterdata selezionati hanno duplicati. Vuoi continuare?", "approving-creation": "<PERSON><PERSON><PERSON><PERSON>"}, "authorization-group": "Authorization Group:", "approver-roles": "Approver Roles:", "process": {"process-notes": "Note di processo per {{processCode}}", "process-notes-empty": "Nessuna cronologia delle note da mostrare"}, "grDelete": {"dialog": {"lbl-message": "Confermi la richiesta di eliminazione del Golden Record?", "closeButton": "Can<PERSON><PERSON>", "confirmButton": "Conferma"}, "modalDetails": {"title": "Conferma Golden Record eliminazione"}}}, "popups": {"titles": {"individual-coll": "Individuale o collettivo non compilato, si prega di fornire un valore", "serial-no-profile": "Il profilo del numero di serie non è stato compilato, si prega di fornire un valore", "mrp-controller": "Il campo \"MRP Controller\" non è popolato, si prega di inserire un valore", "purchasing-group": "La definizione del gruppo d'acquisto non è stata compilato, si prega di fornire un valore", "minimum-ordering-quantity": "La quantità minima di ordine non è stata compilata, si prega di fornire un valore", "material-group": "Gruppo merce non presente o non valido", "goods-receipt-processing-time": "Il tempo di elaborazione delle ricezioni merce non è stato compilato, si prega di fornire un valore", "plant-specific-material-status": "Lo stato specifico della divisione non è stato compilato, si prega di fornire un valore", "margin-key": "Chiave orizzonte per tempi buffer è vuoto, inserire un valore", "size-dimension": "Dimensione non compilata, si prega di inserire un valore", "obsolete-suppliers": "Il fornitore è obsoleto", "missing-material-group": "Il gruppo del materiale è mancante o non valido", "part-weight": "Il peso del componente non è popolato, si prega di inserire un valore", "contracts": "Il contratto è scaduto", "expired-frame-contract": "<PERSON><PERSON><PERSON> sca<PERSON>to", "warranty": "Il campo garanzia non è popolato, si prega di inserire un valore", "hazardous-material": "Dati per il materiali pericolosi mancanti", "inconsistent-mrp-value": "MRP Controller inconsistente, si prega di fornire un valore", "inconsistent-purchasing-group": "Gruppo di acquisto inconsistente, si prega di fornire un laore", "cancel-title": "Cancella attività", "cancel-message": "Sei sicuro di voler cancellare il processo?", "gr-creation-approval": "Approva la Creazione del Golden Record"}, "actions": {"confirm": "Conferma", "cancel": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Cancellato", "delete-gr": "GR Cancellato"}, "only-integer": "*Solo caratteri numerici ammessi"}, "ab-inbev-integration": {"home-page": {"historic-downloads": {"historical-downloads": "Storico Download", "export": "Esporta", "author-download": "Esportato da", "actions": "Azioni", "request-date": "Data richiesta", "client": "Client", "number-of-requests": "Numero di richieste", "status": "Stato", "empty-page": {"title": "Nessun file è stato generato", "subtitle": "I download generati dagli altri servizi appariranno qui"}}, "historic-uploads": {"historical-uploads": "Storico Upload", "upload": "Caricamento", "author-updated": "Carica<PERSON> da", "request-date": "Data richiesta", "number-of-requests": "Numero di richieste", "file-status": "Stato", "empty-page": {"title": "Nessun file è stato generato", "subtitle": "I download generati dagli altri servizi appariranno qui"}, "summary": "<PERSON><PERSON><PERSON>"}, "actions": {"choose-file-to-upload": "Seleziona un file da caricare", "download": "Scarica", "see-details": "<PERSON><PERSON><PERSON>", "generate-excel": "Genera Excel", "stream-pending-processes": "Processi pendenti"}, "indicators": {"historic-downloads": {"title": "Downloads", "subtitle": "File generati precedentemente"}, "historic-uploads": {"title": "Uploads", "subtitle": "File caricati precedentemente"}}}, "file-details-page": {"upload-summary": "<PERSON><PERSON><PERSON> caricamento", "go-back": "Torna al centro download", "refresh": "Aggiorna", "processing": "In Elaborazione", "step-details": {"uploaded-by": "Carica<PERSON> da", "uploaded-on": "Caricato su", "number-of-rows": {"label": "Numero di righe", "description": "{{value}} linee"}, "number-of-processes": {"label": "Numero di processi", "description": "{{value}} processi"}, "elapsed": {"label": "<PERSON><PERSON>", "description": "{{value}} ms"}, "message": {"label": "Messaggio", "description": "{{value}} ms"}}, "step-validation": {"process-started": "Processo iniziato", "file-uploaded": "File caricato", "file-validated": "<PERSON>", "process-validation": "Processo Validato", "process-confirmation": "Conferma Upload", "notify-requesters": "Richiesta di notifica", "completed": "Completato"}}, "file-status": {"processing": "In Elaborazione", "processed": "Processati", "error": "vuoto", "generating": "Generando", "ready-to-be-exported": "Pronto per essere esportato", "waiting-for-feedback": "In attesa di riscontro", "feedback-received": "Feedback ricevuto"}, "errors": {"error-view": {"head-text": "Errore durante l'elaborazione della richiesta"}, "multiple-upload-error": "Non è possibile caricare file multipli", "empty-requests-for-excel-creation": "La generazione del file Excel non può essere effettuata perché nessuna nuova richiesta è stata inserita dopo l'ultimo Excel generato", "empty-requests-for-excel-pending-creation": "La generazione del file Excel per i processi in attesa non può essere effettuata perchè non ci sono nuove richieste inserite dopo l'ultimo Excel generato", "unable-to-retrieve-user-details": "La generazione del file Excel non può essere eseguita perché non è possibile recuperare le informazioni dell'utente. Si prega di riprovare più tardi. ", "default": "Si è verificato un errore, riprovare più tardi", "user-not-authorized-on-any-client": "Nessun processo disponibile per i client dell'utente"}, "upload-details": {"errors": {"missing-material-code": "Numero Materiale mancante per la riga: {{param0}}", "missing-client": "Client mancante per la riga: {{param0}}", "client-cell-mismatch": "Client nel file Excel non corrisponde con il client presente sul master data alla riga: {{param0}}", "client-unauthorized": "Nessuna autorizzazione per il client per la riga: {{param0}}", "no-client-authorized": "Utente non auterizzato su alcun client", "multiple-clients-uploaded": "Client multipli caricati; caricare un Excel per ogni client", "duplicate-material-code": "Material Number duplicato sulla riga {{param0}}; ogni materiale deve avere un differente Material Number", "existing-material-code": "Material Number invalido per la riga: {{param0}}; il materiale esiste già", "error-checking-material-code": "Errore durante la verifica dei Material Number per la riga: {{param0}}; creazione del materiale ignorata, si prega di ricaricare nuovamente questa riga", "null-file": "Il file è mancante", "not-excel": "Il file caricato non è un file Excel", "sheet-number": "Il file deve avere esattamente {{param0}} fogli", "internal-error": "Errore interno: {{param0}}", "cannot-contact-materials": "Non posso contattare il servizio remoto; riprovare più tardi", "header-mismatch": "Errore nell'intestazione, Atteso: '{{param0}}' alla posizione: {{param1}}. Rilevato: '{{param2}}'", "invalid-request-status": "Processo con id: {{param0}} è in stato: {{param1}} e non {{param2}} come atteso", "all-processes-invalid": "Tutti i processi sono validi", "cannot-contact-workflow": "Non posso contattare materials service; provare più tardi"}, "warnings": {"send-email-failure": "Impossibile notificare: {{param0}}"}}}, "languages": {"sq": "Albanese", "ar": "<PERSON><PERSON>", "be": "<PERSON><PERSON><PERSON> (Fiammingo)", "bg": "Bulgaro", "ca": "<PERSON><PERSON>", "zh": "Cinese", "hr": "Croato", "cs": "Ceco", "da": "<PERSON><PERSON>", "nl": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "et": "Estone", "fi": "Completato", "fr": "<PERSON><PERSON>", "de": "Tedesco", "el": "Greco", "hi": "Hindi", "hu": "<PERSON><PERSON><PERSON><PERSON>", "is": "Islandese", "it": "Italiano", "ja": "Giapponese", "ko": "<PERSON><PERSON>", "lv": "Let<PERSON>", "lt": "Lituano", "mk": "Macedone", "ms": "Malese", "mt": "Maltese", "no": "Norvegese", "nb": "Bokmål norvegese", "nn": "Norvegese", "pl": "Polacco", "pt": "<PERSON><PERSON><PERSON>", "ro": "R<PERSON>no", "ru": "<PERSON>", "sk": "Slovacco", "sl": "Sloveno", "sr": "<PERSON><PERSON>", "es": "<PERSON><PERSON><PERSON>", "sv": "<PERSON><PERSON><PERSON>", "th": "Tailandese", "tr": "<PERSON><PERSON><PERSON>", "uk": "Ucraino", "vi": "Vietnamita", "iw": "Ebraico"}, "language": "<PERSON><PERSON>", "notifications": {"errors": {"invalid-cron-expression": "Espressione CRON non valida", "invalid-email": "Email non valida", "invalid-report-name": "Nome report non valido", "tabular-report-not-found": "Report tabulare non trovato."}, "scheduled-reports": {"table-columns": {"report": "Report", "description": "Descrizione", "scheduling": "Schedulazione", "recipients": "Des<PERSON><PERSON><PERSON>", "created-by": "<PERSON><PERSON><PERSON> <PERSON>", "modified-by": "Modificato da", "creation-date": "Data Creazione", "last-modification": "Ultima Modifica", "sql-command": "Comando SQL"}, "actions": {"edit": "Modifica", "delete": "Elimina", "send-now": "Invia ora"}, "title": "Report schedulati", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "no-data-available": "<PERSON><PERSON>un dato disponibile!", "modal": {"create-report": "Crea Report", "edit-report": "Modifica Rapporto", "confirm": "Conferma cancellazione", "prompt": "Sei sicuro di voler cancellare questo report?"}}}, "bulk-extension": {"page-title": "Carica un file di estensione massiva", "download-instructions": "Il file può essere scaricato dalla pagina di ricerca", "drag-drop": "Trascina e rilascia file qui o ", "browse-button-label": "Sfoglia Files", "notes-placeholder": "Note", "upload-button-label": "Caricamento e Validazione", "popup": {"close-button": "<PERSON><PERSON>", "create-workflows-button": "<PERSON><PERSON> flusso di lavoro", "cancel-creation": "<PERSON><PERSON><PERSON>"}, "errors": {"error-title": "Si prega di correggere l'errore di validazione e ricaricare il file", "message-info": "e questo messaggio è ", "row-number": "Si è verificato un errore sulla riga numero", "empty-search": "Impossibile generare il file di estensione massiva per una richiesta di ricerca vuota", "no-results": "Non è stato possibile generare una estensione massiva! nessun risultato disponibile", "max-size": "Impossibile generare la estensione massiva! I risultati della ricerca superano il limite massimo di {{limit}} ", "not-found": "File di Estensione Massiva non trovato", "error-parsing-excel": "Errore durante la lettura del file Excel", "get-plants-authorization": "Errore nel recupero delle autorizzazioni sui plant"}, "success": {"loading": "Creazione flusso di lavoro", "success-title": "<PERSON><PERSON> di Lavoro", "operation-codes-message": " L'operazione è stata presa in carico e ", "processes-creation": "processi in fase di creazione. Si prega di utilizzare questi codici di creazione massiva nel Elenco di Lavoro per identificarli", "oc-new-extensions": "Nuove Estensioni", "oc-extension-updates": "Aggiornamenti estensione", "oc-localized-description": "Aggiornamenti descrizioni Localizzate", "file-stats-description": " il processo sarà creato in toto sull'estensione, o sull'aggiornamento estensione, ", "file-stats-of": "di ", "file-stats-to": " masterdata di ", "file-stats-plants": " plants", "file-stats-localization-description": " saranno creati processi in aggiunta per aggiornare la descrizione dei masterdata", "file-stats-end-note": "Questi processi possono essere approvati singolarmente o in blocco"}}, "massiveEdit": {"status": {"A_STARTING": "Vai", "A_FORMAL_FILE_VALIDATION": "Validazione formale File", "A_EXTRACTING_DATA": "Estrazione Dati", "A_VALIDATING_DATA": "Validazione Dati", "H_CONFIRM_DATA": "Verifica e conferma dati", "A_EDIT_PROCESS_GENERATION": "Generazione processi", "H_COMPLETED": "Completo", "H_ABANDONED": "Abbandonato", "H_TERMINATED": "Terminato", "END": "Fine"}, "fieldStatus": {"IGNORED": "<PERSON><PERSON><PERSON>", "CONFIRMED": "Confermato", "INVALID": "Invalido", "NOT_CHANGED": "Non modificato", "NOT_EDITABLE": "Non modificabile", "VALIDATED": "Validato", "DRAFT": "<PERSON><PERSON>"}, "list": {"searchForm": {"actions": {"label": "<PERSON><PERSON><PERSON><PERSON>", "toggleAutorefresh": "Auto-Aggiorna", "startProcess": "Carica", "search": "Cerca", "toggleAutorefresh_false": "Attiva Auto Aggiorna", "toggleAutorefresh_true": "Disattiva Auto Aggiorna ({{interval}})"}, "dateFilter": {"label": "Data Caricamento", "placeholder": "Seleziona range di date"}, "status": {"label": "Stato", "placeholder": "Seleziona Stato"}, "user": {"label": "Carica<PERSON> da", "placeholder": "Seleziona utente"}}, "table": {"columns": {"processId": "Id", "uploadedBy": "Car. Da", "uploadDate": "Data. Car.", "filename": "Nome File", "processSummary": "<PERSON><PERSON><PERSON>", "currentStatus": "Stato", "records": "N. Records", "actions": " ", "worklist-ref": "ID processo Massivo", "groups": "Gruppi"}}, "modals": {"startProcessModal": {"header": "Inizia nuovo processo...", "btnConfirm": "Conferma"}, "processCreatedDialog": {"successMsg": "Process {{processId}} creato con successo!", "btnClose": "<PERSON><PERSON>", "btnOpenProcess": "<PERSON>i"}}}, "processDetail": {"actions": {"show_history": "Storico", "toggle_more_info": "<PERSON><PERSON> informazioni", "backToList": "Ritorna a lista processi", "export": "Esporta", "abandon": "<PERSON><PERSON><PERSON><PERSON>", "reload": "Aggiorna", "reload_auto": "Aggiorna ({{countdown}})", "save": "<PERSON><PERSON>", "next": "Prossimo", "H_CONFIRM_DATA": {"next": "In. Proc.", "next_clear": "Inizia Processi di Edit", "next_dirty": "In attesa del salvataggio manuale dell'utente"}}, "processInfo": {"labels": {"processId": "<PERSON><PERSON>", "uploadedBy": "Carica<PERSON> da", "uploadDate": "Data Caricamento", "file_name": "Nome File", "currentStatus": "Stato Attuale", "records": "Records", "bulkProcessId": "ID processo Massivo", "groups": "Gruppi"}, "history": {"active": "Attivo", "tmsStart": "Iniziato", "tmsEnd": "<PERSON><PERSON>", "tms_insert": "Data Inserimento", "user_insert": "<PERSON>ser<PERSON>", "tms_update": "Data Aggiornamento", "user_update": "Aggiornato Da", "title": "Storia", "show-all-substate": "Mostra tutti i sottostati"}}, "steps": {"generic": {"default-message-text": "In attesa che i processi automatici aggiornino i dati..."}, "H_CONFIRM_DATA": {"labels": {"masterdata_header": "Masterdata da aggiornare", "masterdataId": "Tam ID", "client_code": "Mandante / Codeice", "masterdata_header_missing_materials": "I Master Data mancanti non hanno modifiche"}, "fieldTable": {"toggleFieldFilter": {"true": "Nascondi  campi ignorati dalla IA", "false": "Mostra  campi ignorati dalla IA"}, "fieldName": "Campo", "changes": "Modifiche", "status": "Stato", "newValue": "Nuovo Valore", "oldValue": "<PERSON><PERSON><PERSON><PERSON>"}}, "A_EDIT_PROCESS_GENERATION": {"waiting-message": "In attesa che i processi di modifica vengano generati..."}, "END": {"H_TERMINATED_message": "Processo terminato automaticamente! Some critical error occurred.", "H_COMPLETED_message": "Processo completato!", "H_ABANDONED_message": "Processo abbandonato dall'utente", "table": {"header": {"materialKey": "Codice", "materialDescr": "Descrizione", "processCode": "Esito"}, "messages": {"process-not-generated": "Nessun processo generato!", "process-generated": "Processo <b>{{materialProcessId}}</b> lanciato"}}}}}, "errors": {"extraction-purpose-metadata-mandatory": "Il file caricato non è valido. Esportare un template di tipo Massive Edit", "language-metadata-mandatory": "Il metadato lingua è obbligatorio", "customer-id-metadata-mandatory": "La proprietà CustomerId e obbligatoria.", "unable-to-read-input-file": "Impossibile leggere il file di input.", "input-file-mandatory": "Il file excel di input è obbligatorio", "user-id-mandatory": "L'ID utente è obbligatorio.", "language-mandatory": "La lingua è obbligatoria.", "validation-error": "Errore di convalida", "unable-to-save-input-file": "Impossibile leggere il file di input.", "unable-to-start-workflow": "Impossibile avviare il flusso di lavoro."}, "messages": {"title": {"extraction-purpose-error": "Tipo di estrazione errato", "extraction-purpose-success": "Tipo di estrazione", "language-error": "Lingua errata", "language-success": "<PERSON><PERSON>", "customer-id-error": "<PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON>", "customer-id-success": "ID Cliente", "sheet-number-error": "Numero di fogli errato", "sheet-number-success": "Numero fogli", "wrong-column-position-error": "Posizione colonna errata", "wrong-column-position-success": "Posizione colonna"}, "body": {"extraction-purpose-error": "Il template di esportazione non è valido.", "extraction-purpose-success": "Il template di esportazione è valido", "language-error": "Lingua non valida.", "language-success": "La lingua è valida.", "customer-id-error": "Id Cliente non valido.", "customer-id-success": "ID Cliente valido.", "sheet-number-error": "Il file ha il numero sbagliato di fogli.", "sheet-number-success": "Il file ha il numero corretto di fogli.", "wrong-column-position-error": "Una colonna è nella posizione sbagliata.", "wrong-column-position-success": "<PERSON>tte le colonne sono al posto giusto."}}, "internalStatus": {"PENDING": "IN SOSPESO", "WORKING": "LAVORANDO", "COMPLETED": "COMPLETATO", "ERROR": "ERRORE"}}, "generic": {"tables": {"norowmessage": "Nessuna riga trovata", "loading-message": "<PERSON><PERSON><PERSON> in caricamento"}, "messages": {"operation-success": "Operazione completata con successo!", "error": {"title": "Si è verifcato un errore generico", "body": "Un errore generico si è verificato nel server. Si prega di contattare il supporto"}, "no-user-rights": {"title": "Vietato!", "body": "L'utente non ha i permessi per questa azione!"}}}, "admin-page": {"enel-plugin": {"menu": {"root": "<PERSON><PERSON>", "check-auth-tests": "Verifica gruppo autor."}}}, "no-changes-allowed": {"messages": {"error": {"title": "Nessuna modifica da applicare", "body": "Nessuna delle modifiche sul masterdata sono permesse su campi non editabili."}}}, "supplier": {"action": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "update": "Aggiorna fornitore", "delete": "Elimina fornitore", "list": "Lista fornitori"}, "button": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload-supplier": "Caricamento file", "download-template": "Scarica Template", "details": "<PERSON><PERSON><PERSON>", "edit": "Modifica", "delete": "Elimina"}, "title": {"empty": "<PERSON><PERSON><PERSON>", "list": "Lista di fornitori", "edit": "Modifica Fornitore", "create": "<PERSON><PERSON>"}, "popup": {"title": "<PERSON><PERSON><PERSON>", "message": "Codice fornitore già esistente in questo cliente:{{client}} {{code}}"}, "response_server": {"operation-finished-successfully": "Operazione completata con successo", "supplier_already_in_use": "Fornitore già in uso", "supplier_already_exist": "Il fornitore esiste già {{client}} {{code}}", "supplier_not_present": "Il fornitore non dovrebbe essere vuoto"}, "delete-confirm": {"title": "Elimina il fornitore", "message": "Questo fornitore non viene mai utilizzato in TAM. Confermi l'eliminazione?"}, "excel-import": {"report": "Importazione fornitori da report Excel", "created": "<PERSON><PERSON><PERSON>", "updated": "Aggiornato", "deleted": "Eliminato", "error": "Errore", "row": "Numero di riga"}, "client": "Client", "manufacturerCode": "Codice produttore", "locationId": "ID Locazione", "createdDate": "Data creazione", "createdBy": "<PERSON><PERSON><PERSON> <PERSON>", "lastUpdatedDate": "Data ultimo aggiornamento", "updatedBy": "Aggiornato da", "manufacturerName": "Nome del produttore", "vatRegistrationNumber": "Numero di registrazione", "contactLastName": "Cognome contatto", "manufacturerPartNumber": "Numero di parte del produttore", "countryCode": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON>", "name2": "Nome2", "name3": "Nome3", "name4": "Nome4", "supplierGroupNormalized": "Gruppo Fornitori Normalizzato", "supplierNameNormalized": "Nome Fornitore <PERSON>"}, "smartCreation": {"dynamic-components": {"no-value-selected": "<<Nessun valore selezionato>>"}, "attachments": {"label": "Allegati", "addAttachment": "Aggiugi un allegato", "no-attachment": "<PERSON><PERSON><PERSON> allegato"}, "classification": {"label": "Classificazione"}, "descriptions": {"shortDescriptions": "Descrizione breve", "longDescriptions": "Descrizione lunga"}, "fields": {"mandatoryFields": "Ci sono campi obbligatori", "fetch-value-error": "Impossibile recuperare l'errore per il campo {{fieldName}}"}, "validation": {"checkFields": "Controlla i campi", "checkWarnings": "Controlla avviso sui campi"}, "categories": {"dialog": {"closeButton": "<PERSON><PERSON>", "codeFilter": "Codice", "confirmButton": "Conferma", "descriptionFilter": "Descrizione", "filter": "Filtra", "title": "Seleziona una categoria", "warning": "Seleziona una categoria prima di confermare", "currentSelection": "Selezionato:"}, "title": "Categorie", "emptyCategories": "Nessuna categoria trovata", "selectManually": "Seleziona manualmente", "manuallySelectedCategoriesWarning": "Le categorizzazioni su <b>{{categories}}</b> proposte da TAM hanno una probabilità del 95% di essere corrette. Sei sicuro di volerne selezionare un altro?", "reset": ""}, "domain": {"label": "<PERSON>inio"}, "completeness": {"label": "Completezza"}, "materialImage": {"label": "Immagine materiale"}, "materialName": {"label": "Nome materiale"}, "goldenRecordCode": {"label": "Codice Golden Record"}, "smartCreation": {"title": "Smart creation", "previous": "Indietro", "next": "<PERSON><PERSON>", "search": "Cerca", "validate": "Valida", "plantData": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notFound": "Non sono stati trovati plants", "duplicatePlantKeyError": "Plant {{plant_key}} già presente", "plant-added": "Dati dell'impianto {{plantKey}} aggiunti/aggiornati.", "plant-removed": "Dati dell'impianto {{plantKey}} rimossi."}, "validation": {"error": "Errore validazione"}, "backToValidationDialog": {"header": "Alcune operazioni sono in sospeso", "message": "Tornare alla convalida reimposterà tutti i campi. Vuoi procedere?", "acceptLabel": "Sì", "rejectLabel": "No"}, "copy-material": {"info": "<PERSON><PERSON><PERSON> <PERSON>"}}, "smartValidation": {"mandatory": "Il campo è obbligatorio", "autocomplete": {"selected-items-group": "Selezionato", "filtered-group": "Elementi disponibili"}, "no-items-in-list": "Nessun elemento nell'elenco", "domain": {"label": "<PERSON>inio"}, "title": "Smart Validation", "search": "Cerca", "reset": "Reset", "next": "<PERSON><PERSON>", "description": {"label": "Descrizione"}, "error": "Errore", "normalizedDescriptions": {"title": "Descrizione normalizzata"}, "select": "Seleziona", "selectFilters": {"label": "Filtri di selezione", "similarMaterials": "Materiali simili", "notFound": "Nessun filtro trovato per la descrizione inserita"}, "similarMaterials": {"client": "Client", "description": "Descrizione", "hideDuplicates": "Nascondi duplicati", "hideGoldenRecordInstances": "Nascondi istanze di golden record", "hideObsolete": "Nascondi obsoleti", "includeGoldenRecords": "<PERSON>ludi golden records", "includeMaterialsFromOtherClients": "Includi materiali da altri Client", "materialCode": "Codice Materiale", "notFound": "Nessun materiale trovato", "country": "<PERSON><PERSON>", "plants": "Impiant<PERSON>"}, "technicalAttributes": {"title": "Attributi tecnici"}, "client": {"label": "Cliente"}, "gr": {"toCreate": "<PERSON><PERSON>", "plant": "Plant", "noplant": "Non sono presenti plant", "client": "Client", "plantPanel": {"title": "Seleziona plant per il client ", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON>", "error": "Controllare i campi obbligatori"}, "enableCreation": "Abilita creazione", "autoExtensionPlant": "Auto estensione plant", "confirmOpenModal": {"header": "Alcune operazioni sono in corso", "message": "La sezione IMPIANTO è parzialmente compilata. I dati non confermati and<PERSON>no persi, vuoi procedere?", "acceptLabel": "Si", "rejectLabel": "No"}}, "gr-governed-attribute": "Campo gestito da Golden Record", "mandatory-error": "{{field}} è vuoto", "manufacturerCodeIsAlreadyInUse": "Il valore è già utilizzato su questo cliente in combinazione con un altro valore", "manufacturerPartNumberIsAlreadyInUse": "Il valore è già utilizzato su questo cliente in combinazione con un altro valore", "attributeCodeIsAlreadyInUse": "L'attributo è già utilizzato su questo cliente in combinazione con un altro attributo", "attributeBrandIsAlreadyInUse": "L'attributo è già utilizzato su questo cliente in combinazione con un altro attributo", "plantRequired": "Plant richiesto", "materialCodeIsAlreadyInUse": "Material code è già utilizzato", "oldMaterialNumberIsAlreadyInUse": "Code è già utilizzato", "copy": {"typeError": "Copy non disponibile sul Golden Record"}}, "smartSummary": {"title": "Smart Summary", "previous": "Indietro", "create": "<PERSON><PERSON>", "submit": "Submit"}, "modalDetails": {"title": "Salva materiale", "hideFieldNoValueButton": "Nascondi attributi vuoti", "showFieldNoValueButton": "Visualizza attributi vuoti", "alternativeUnitOfMeasurement": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON>", "notFound": "Alternative units of measurement non trovato", "checkFields": "Controlla tutti i campi", "of": "di", "confirm": "Conferma"}, "closeButton": "<PERSON><PERSON>", "validateButton": "Convalida", "confirmButton": "Conferma", "confirmationMessage": "Materiale salvato correttamente", "confirmationMessageProcessCode": "La richiesta è stata creata correttamente con codice processo: ", "savedTitle": "Salva materiale", "savedConfirmButton": "Ok", "saveButton": "<PERSON><PERSON>", "approveButton": "<PERSON><PERSON><PERSON><PERSON>", "rejectButton": "<PERSON><PERSON><PERSON>", "updateButton": "Modifica", "additionalInfoButton": "Info aggiuntive", "confirmDialog": {"checkFields": "Il campo è obbligatorio!"}, "header": {"view": "Visualizza", "details": "<PERSON><PERSON><PERSON>", "edit": "Modifica", "gr-edit": "Modifica Golden Record", "approve": "Approve edit", "enrichment": "Enrichment", "instance-of": "Riferimento Golden Record", "description": "Descrizione", "gr-edit-details": "Modifica Golden Record Dettagli di Processo", "gr-approve": "Approve Golden Record", "gr-enrichment": "Golden Record enrichment", "process-details-edit": "Enrichment Dettagli di Processo", "gr-deletion": "Elimina Golden Record", "approval-gr-deletion": ""}, "linkUnlink": {"link-instances": "<PERSON> istanze", "unlink-instances": "Unlink istanze", "search-instances": "Cerca istanze per il link", "noItemsSelected": "Bisogna selezionare almeno un materiale per il link", "confirm": {"title": "Link/Unlink istanze da <PERSON> Record", "message": "Sei sicuro di voler continuare?", "linkMessage": "I seguenti materiali verranno aggiunti a {{goldenRecordCode}} tramite un processo di Golden Record Edit", "unlinkMessage": "I seguenti materiali verranno rimossi da {{goldenRecordCode}} tramite un processo di Golden Record Edit", "confirmButton": "Conferma"}, "search": {"btn": "Cerca", "label": "Codice o descrizione", "emptyText": "Inserisci un testo per la ricerca", "placeholder": "Inserisci il material code o la descrizione da cercare"}, "description": "Descrizione", "approval-gr-deletion": "Approvazione della cancellazione di golden record"}, "deleteButton": "Elimina"}, "suggestionsAttribute": {"copyTooltip": "Copia valore"}, "rejectPossibilities": {"incorrectDataGroup": "Master Data Group errato/non corretto", "missingManufacturerInfo": "Informazioni sul produttore mancanti", "incorrectNamingConvention": "Naming Convention errata", "translationNotProvided": "Traduzione non fornita", "potentialDuplicate": "Potenziale duplicato", "incorrectUoM": "UoM non corretta", "missingTechnicalAttributes": "Attributi tecnici o codice catalogo mancanti", "other": "Altro"}, "createdMaterial": {"dialog": {"title": "Materiale Creato", "confirmButton": "Ok", "confirmationMessage": "La richiesta è stata creata correttamente", "confirmationMessageProcessCode": "La richiesta è stata creata correttamente con codice processo: "}}, "confirmCreation": {"dialog": {"title": "Aggiungi un commento per l'approvatore", "closeButton": "Can<PERSON><PERSON>", "confirmButton": "Conferma", "lbl-message": "Note sul processo "}}, "approval": {"title": "Richiesta di approvazione", "note": "<PERSON>a", "ignoredCategories": "Categorie Ignorate", "taxonomy": "Tassonomia", "additionalInformation": "Informazioni Aggiuntive", "suggestCategories": "Categorie Suggerite", "currentCategory": "Categoria Selezionata", "otherInfo": "Altre Informazioni", "noInformation": "Nessuna informazione disponibile", "smartValidation": "Smart Validation:", "smartCreation": "Smart Creation:", "smartSummary": "Summary:", "rejectLabel": "<PERSON><PERSON><PERSON><PERSON>", "approveLabel": "Conferma", "additionalInfoLabel": "Informazioni aggiuntive", "reject": {"dialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "chooseRejectCause": "Scegli una causa  (obbligatorio)", "comment": "Commento", "closeButton": "<PERSON><PERSON><PERSON>", "confirmButton": "Conferma", "terminateButton": "Termina", "similarMaterial": "Materiale Simile", "note": "Si prega di notare che tutte le modifiche and<PERSON>no perse."}}, "additionalInfo": {"dialog": {"title": "Informazioni aggiuntive"}, "supplier": {"action": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "update": "Aggiorna fornitore", "delete": "Elimina fornitore", "list": "Lista fornitori"}, "button": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload-supplier": "Caricamento file", "details": "<PERSON><PERSON><PERSON>", "edit": "Modifica", "delete": "Elimina", "download-template": "Scarica Template"}, "title": {"empty": "<PERSON><PERSON><PERSON> for<PERSON>", "list": "Lista di fornitori", "edit": "Modifica Fornitore", "create": "<PERSON><PERSON>"}, "popup": {"title": "<PERSON><PERSON><PERSON>", "message": "Codice fornitore già presente in questo client:{{client}} {{code}}"}, "response_server": {"operation-finished-successfully": "Operazione completata con successo", "client_not_found": "Client non trovato", "manufacturer_code_not_found": "Codice produttore non trovato", "manufacturer_name_not_found": "Nome del produttore non trovato", "supplier_already_exist": "Il fornitore esiste già {{client}} {{code}}", "supplier_already_in_use": "<PERSON><PERSON><PERSON> dup<PERSON>", "supplier_not_present": "Fornitore non presente"}, "delete-confirm": {"title": "Elimina il fornitore", "message": "Questo fornitore non è in uso in TAM. Confermi la cancellazione?"}, "excel-import": {"report": "Importazione fornitori da report Excel", "created": "<PERSON><PERSON><PERSON>", "updated": "Aggiornato", "deleted": "Eliminato", "error": "Errore", "row": "Numero di riga"}, "client": "Client", "manufacturerCode": "Codice produttore", "locationId": "ID Locazione", "createdDate": "Data creazione", "createdBy": "<PERSON><PERSON><PERSON> <PERSON>", "lastUpdatedDate": "Data ultimo aggiornamento", "updatedBy": "Aggiornato da", "manufacturerName": "Nome del produttore", "vatRegistrationNumber": "Numero di registrazione", "contactLastName": "Cognome contatto", "manufacturerPartNumber": "Numero di parte del produttore", "countryCode": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON>", "name2": "Nome2", "name3": "Nome3", "name4": "Nome4", "supplierGroupNormalized": "Gruppo Fornitori Normalizzato", "supplierNameNormalized": "Nome Fornitore <PERSON>", "field_required": "Campo richiesto!"}}, "approve": {"dialog": {"title": "Conferma"}}}, "confirmedCreation": {"dialog": {"title": "Le informazioni aggiuntive sono state correttamente aggiunte al materiale"}}, "confirmedEdit": {"dialog": {"title": "Modifica materiale"}, "no-changes-allowed": {"messages": {"error": {"title": "titolo", "body": "corpo"}}}, "supplier": {"action": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "update": "aggiornare", "delete": "Elimina", "list": "Lista"}, "button": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload-supplier": "<PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "edit": "Modifica", "delete": "Elimina", "download-template": "Scarica Template"}, "title": {"empty": "vuoto", "list": "Lista", "edit": "Modifica", "create": "<PERSON><PERSON>"}, "popup": {"title": "titolo", "message": "messaggio"}, "response_server": {"operation-finished-successfully": "operazione completata con successo", "supplier_not_present": "fornitore non presente", "supplier_already_exist": "fornitore duplicato", "supplier_already_in_use": "<PERSON><PERSON><PERSON> dup<PERSON>"}, "delete-confirm": {"title": "titolo", "message": "messaggio"}, "excel-import": {"report": "Rapporto", "created": "<PERSON><PERSON><PERSON>", "updated": "aggiornare", "deleted": "Eliminato", "error": "Errore", "row": "riga"}, "client": "Client", "manufacturerCode": "Codice produttore", "locationId": "ID Locazione", "createdDate": "Data creazione", "createdBy": "<PERSON><PERSON><PERSON> <PERSON>", "lastUpdatedDate": "Data ultimo aggiornamento", "updatedBy": "Aggiornato da", "manufacturerName": "Nome del produttore", "vatRegistrationNumber": "Partita IVA", "contactLastName": "Cognome contatto", "manufacturerPartNumber": "Numero di parte del produttore", "countryCode": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON>", "name2": "Nome 2", "name3": "Nome 3", "name4": "Nome 4", "supplierGroupNormalized": "Gruppo Fornitori Normalizzato", "supplierNameNormalized": "Nome Fornitore <PERSON>"}, "confirmedUnlink": {"dialog": {"title": "Il processo di Unlink Golden Record Edit è stato creato"}}, "confirmedLink": {"dialog": {"title": "Il processo di Link Golden Record Edit è stato creato"}}}, "materialModal": {"changes": {"title": "Cambiamenti", "attribute": "Attributi", "previousValue": "<PERSON><PERSON>e", "editType": "Tipo di cambiamento", "currentValue": "<PERSON><PERSON> corrente", "viewer": {"title": "Dettaglio", "open": "<PERSON>i"}}, "linkunlink": {"title": "Istanze da aggiungere o rimuovere", "label": {"link": {"tooltip": "Il materiale verrà aggiunto al Golden Record", "badge": "<PERSON><PERSON><PERSON><PERSON>"}, "unlink": {"tooltip": "Il materiale verrà rimosso dal Golden Record", "badge": "<PERSON><PERSON><PERSON>"}}}}, "materialCodeGr": {"label": "Codice golden record"}, "copyMaterial": {"label": "<PERSON><PERSON><PERSON> <PERSON>"}, "show-comments": "Mostra i commenti del processo:", "confirmedLink": {"dialog": {"title": ""}}, "confirmedUnlink": {"dialog": {"title": ""}}}, "role-management": {"list": {"pageTitle": "Gestione ruoli", "searchPlaceholder": "Cerca per nome...", "roleName": "Nome ruolo", "linkedAccount": "Account collegati", "linkedService": "Ser<PERSON><PERSON> collegati", "linkedGroup": "Gruppi collegati"}, "deleteConfirmationHeader": "Canferma cancellazione", "deleteConfirmationMessage": "Vuoi cancellare il ruolo selezionato?", "noSelection": "Selezionare almeno un ruolo!", "deleteSuccess": "Cancellazione avvenuta con successo", "createSuccess": "Creazione avvenuta con successo", "filters": {"addNew": "Nuovo", "actions": {"title": "Azioni", "delete": "Elimina"}, "export": "Esporta"}, "edit": {"basicProfile": "Basic", "EDIT": "Modifica Ruolo", "advancedProfile": "Advanced", "basic": "Modifica dati base", "roles": "Modifica ruoli", "administratorProfile": "Administrator", "createRole": "<PERSON><PERSON> ruolo", "name": "Nome", "placeholderName": "<PERSON><PERSON>", "roleId": "ID ruolo", "placeholderRoleId": "ID", "internal": "Interno", "profileType": "Tipo profilo", "comment": "Commento", "accounts": {"title": "Accounts", "notfound": "Account non trovati"}, "groups": {"title": "Gruppi", "notfound": "Gruppi non trovati"}, "services": "<PERSON><PERSON><PERSON>", "requesterRoles": "Requester Roles", "requesterCreationCustomRoles": "Requester Creation Custom Roles", "requesterEditCustomRoles": "Requester Edit Custom Roles", "approverRoles": "Approver Roles", "approverCustomRoles": "Approver Custom Roles", "duplicateCustomRoles": "Duplicates Custom Roles", "enabledActions": "Enabled Actions", "buttons": {"close": "<PERSON><PERSON>", "create": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "edit": "Modifica", "clone": "Clona"}, "checkRequiredFields": "Inserire i campi obbligatori!", "DETAILS": "<PERSON><PERSON><PERSON> ruolo", "CREATE": "Crea nuovo ruolo", "DETAIL": "Dettagli del ruolo", "CLONE": "<PERSON><PERSON><PERSON> ruolo", "organizations": "Organizzazioni", "organizations_available": "Organizzazioni disponibili", "organizations_selected": "Organizzazioni selezionate"}, "service-list": {"legenda": "<PERSON>a", "enableService": "Abilita servizio", "disableService": "Disabilita servizio", "resetService": "<PERSON><PERSON><PERSON><PERSON> servizio", "enableServiceAndMakeAdmin": "Abilita e rendi amministratore", "cercaServiziPlaceholder": "Cerca servizi...", "azioni": "Azioni", "deseleziona": "Deseleziona", "seleziona": "Seleziona", "auth_level": {"enabled": "Abilitato", "disabled": "Disabilitato", "administrator": "Amministratore", "notAffected": "Non coinvolto", "readOnly": "Sola lettura"}, "readOnlyService": "Sola lettura"}}, "custom-roles": {"list": {"pageTitle": "Custom Roles", "searchPlaceholder": "Cerca per nome...", "name": "Nome", "processType": "Process Type", "description": "Descrizione"}, "deleteConfirmationHeader": "Canferma cancellazione", "deleteConfirmationMessage": "Vuoi cancellare il custom role selezionato?", "noSelection": "Selezionare almeno un ruolo!", "filters": {"addNew": "Nuovo", "actions": {"title": "Azioni", "delete": "Elimina"}, "export": "Esporta"}, "edit": {"processType": {"workflowCreation": "Workflow Creazione", "workflowEnrichment": "Workflow Arricchimento", "workflowApproval": "Workflow Approvazione", "workflowDuplicate": "Workflow Duplicati"}, "createGroup": "<PERSON>rea gruppo", "roleId": "ID ruolo", "name": "Nome ruolo", "namePlaceholder": "Nome ruolo", "profileType": {"title": "Tipo profilo", "emptyOption": "Seleziona..."}, "comment": "Commento", "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "save": "<PERSON><PERSON>"}, "requiredInformationInformation": "Inserire le informazioni obligatorie"}, "editConfirmHeader": "Le informazioni modificate per il custom role \"{{role}}\" ({{roleId}}) saranno salvate", "editConfirmMessage": "Vuoi procedere?", "createConfirmHeader": "Il ruolo custom \"{{role}}\" sarà creato", "createConfirmMessage": "Vuoi procedere?"}, "accounts-management": {"create": {"title": "Nuovo utente", "subtitle": "Crea nuovo utente da zero..."}, "filters": {"addNew": "Nuovo", "actions": {"title": "Azioni", "delete": "Elimina", "resendActivationMail": "Reinvia mail di attivazione", "forceResetPassword": "Reset password", "changeRoleTo": "Cambia Ruolo…", "changeRole": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "change": "Modifica", "remove": "Elimina"}, "assignRole": "Modifica Ruolo(i)", "ClearSelection": "<PERSON><PERSON><PERSON>", "anonymize": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "export": "Esporta"}, "noSelection": "Selezionare almeno un utente!", "resendActivationMailConfirmationHeader": "Comferma reinvio attivazione", "resendActivationMailConfirmationMessage": "Vuoi reinviare la mail di attivazione per gli account selezionati?", "deleteConfirmationHeader": "Canferma cancellazione", "deleteConfirmationMessage": "Vuoi cancellare gli account selezionati?", "forceResetPasswordConfirmationHeader": "Conferma reset password", "forceResetPasswordConfirmationMessage": "Vuoi resettare la password per gli acocunt selezionati?", "changeRoleToConfirmationHeader": "Conferma cambio ruolo", "changeRoleToConfirmationMessage": "Vuoi cambiare il ruolo per gli account selezionati?", "checkRequiredFields": "Inserire i campi obbligatori!", "list": {"pageTitle": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Cerca per nome…", "displayName": "Nome visualiz<PERSON>", "login": "<PERSON><PERSON>", "group": "Gruppo", "securityRole": "<PERSON><PERSON><PERSON>", "status": "Stato", "system": "Sistema"}, "form": {"noCountryAssociated": "<PERSON><PERSON><PERSON> paese associato", "noRoleAssociated": "<PERSON><PERSON><PERSON> ruolo associato", "requiredInformationInformation": "Inserire le informazioni obligatorie", "invalidEmail": "Mail non valida", "label": {"password": "Password", "system": "System", "id": "Id", "givenName": "Nome", "lastName": "Cognome", "displayName": "Nome visualiz<PERSON>", "securityRoles": "<PERSON><PERSON><PERSON>", "inheritedRoles": "<PERSON><PERSON><PERSON> er<PERSON>", "inheritRoles": {"empty": "<PERSON><PERSON><PERSON>"}, "ignoreCreationEmail": "Ignora creazione email", "ignoreEnrichmentEmail": "Ignora modifica email", "ignoreApprovalEmail": "Ignora approvazione email", "email": "Email", "company": "Company", "comment": "Commento", "login": "<PERSON><PERSON>", "group": "Gruppo", "country": "<PERSON><PERSON>", "language": "<PERSON><PERSON>", "decimalSeparator": "Separatore decimale", "department": "Dipartimento", "internal": "Interno", "securityRoles_available": "Disponibile", "securityRoles_selected": "Selezionato", "status": "Stato", "targetUsers": "Utente target", "inheritedOrganizations": "Organizzazioni ereditate", "organizations": "Organizzazioni", "organizations_available": "Disponibile", "organizations_selected": "Selezionato"}, "button": {"cancel": "<PERSON><PERSON><PERSON>", "save": "Modifica", "create": "<PERSON><PERSON>"}, "resetPassword": "Reset password", "lastLogin": "Ultimo accesso", "memberSince": "Data iscrizione", "title": {"organizationConfiguration": "Organizzazione", "actions": "Azioni", "securityInfo": "Informazioni di sicurezza", "sendEmailPermissions": "Permessi invio mail", "credentials": "Credenziali"}, "info": {"login": "Nota bene: una password generata verrà inviata all'utente che dovrà cambiare la password al primo accesso."}, "default": {"country": "Seleziona Nazione...", "selectMessage": "Seleziona...", "securityRoles": "Select security roles..."}, "client": "Clienti", "businessUnit": "Business unit", "requesterRoles": "Requester Roles", "requesterCreationCustomRoles": "Creazione Ruoli Custom", "requesterEditCustomRoles": "Modifica Ruoi Custom", "approverRoles": "Approvatore", "approverCustomRoles": "Approvatore Ruoli Custom", "duplicatesCustomRoles": "Duplicazione Ruoli Custom", "enabledActions": "Azioni abilitate", "blockAccount": "Blocca account", "unblockAccount": "Sblocca account", "resendActivationMail": "Reinvia mail di attivazione", "fieldMinLength": "Lunghezza minima {{minlength}} caratteri.", "requiredField": "Campo obbligatorio!", "treeEmptyMessage": "<PERSON><PERSON><PERSON> dato disponibile", "decimalSeparatorLang": {"en-US": "en-US -> 123,456.789", "it-IT": "it-IT -> 123.456,789"}}, "deleteSuccess": "Cancellazione avvenuta con successo", "createSuccess": "Creazione avvenuta con successo", "genericSuccess": "Operazione eseguita con successo", "blockAccountConfirmationHeader": "Conferma blocco account", "blockAccountConfirmationMessage": "Vuoi bloccare questo account?", "unblockAccountConfirmationHeader": "Conferma sblocco account", "unblockAccountConfirmationMessage": "Vuoi sbloccare questo account?", "title": {"EDIT": "Modifica Account", "DETAILS": "Dettagli account", "CREATE": "Crea nuovo account", "DETAIL": "Dettagli del conto", "CLONE": "Clona account"}, "edit": {"buttons": {"close": "<PERSON><PERSON>", "create": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "edit": "Modifica", "continue": "Continua", "clone": "Clona"}, "basic": "Informazioni Base", "additionalData": "Informazioni aggiuntive", "preferences": "Prefer<PERSON><PERSON>", "security": "<PERSON><PERSON><PERSON>"}, "userEditConfirmHeader": "Le informazioni dell'account modificate per \"{{displayName}}\" ({{login}}) saranno salvate.", "userEditConfirmMessage": "Vuoi procedere?", "userCreateConfirmHeader": "L'account \"{{displayName}}\" ({{login}}) sarà creato", "userCreateConfirmMessage": "Vuoi procedere?", "dialog": {"buttons": {"acceptLabel": "Accetta", "rejectLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "applyRole": {"header": "Seleziona il/i ruolo(i) da applicare"}}, "confirmation-messages": {"ResetPassword_list": {"header": "Conferma reset password", "message": "Vuoi reimpostare la password per questi {{selectedCount}} account?"}, "ResendActivationMail_list": {"header": "Comferma reinvio attivazione", "message": "Vuoi rinviare l'email di attivazione per questi {{selectedCount}} account?"}, "LockAccount_list": {"header": "Conferma blocco accounts", "message": "Vuoi bloccare questi {{selectedCount}} account?"}, "UnlockAccount_list": {"header": "Conferma sblocco accounts", "message": "Vuoi sbloccare questi {{selectedCount}} account?"}, "AssignRole_list": {"header": "Conferma assegnazione ruolo(i)", "message": "Vuoi assegnare il/i ruolo/i <strong>{{rolesToApply}}</strong> a questi {{selectedCount}} account?"}, "AssignGroup_list": {"header": "Conferma assegnazione gruppo", "message": "Vuoi assegnare il gruppo {{selected.name}} a questi {{selectedCount}} account?"}, "Delete_list": {"deleteConfirmationHeader": "Conferma Cancellazione", "deleteConfirmationMessage": "Vuoi cancellare questi {{selectedCount}} accounts?", "header": "Elimina conferma", "message": "Vuoi eliminare questi {{selectedCount}} account?"}, "Anonymize_list": {"header": "Conferma l'anonimizzazione dei conti", "message": "Vuoi anonimizzare questi {{selectedCount}} account?"}}, "anonymizeSuccess": "Anonimizzazione riuscita"}, "general": {"messages": {"copy-to-clipboard": "Testo copiato negli appunti:"}, "placeholders": {"dropdown-not-selected": "Seleziona un valore"}, "values": {"empty-string": "«vuoto»"}}, "group-management": {"idGroup": "ID Gruppo", "list": {"pageTitle": "Gestione Gruppi", "searchPlaceholder": "Cerca per nome…", "accountsNumber": "Account collegati", "emptyMessage": "<PERSON><PERSON>un gruppo in archivio", "name": "Nome", "internal": "Interno"}, "deleteConfirmationHeader": "Canferma cancellazione", "deleteConfirmationMessage": "Vuoi cancellare il gruppo selezionato?", "noSelection": "Selezionare almeno un gruppo!", "deleteSuccess": "Cancellazione avvenuta con successo", "createSuccess": "Creazione avvenuta con successo", "filters": {"addNew": "Nuovo", "actions": {"title": "Azioni", "delete": "Elimina"}, "export": "Esporta"}, "edit": {"createGroup": "<PERSON>rea gruppo", "requiredInformationInformation": "Inserire le informazioni obligatorie", "idGroup": "ID gruppo", "name": "nome", "namePlaceholder": "Nome del gruppo", "description": "descrizione", "internal": "Interno", "securityInfo": "Informazioni di sicurezza", "securityRoles": "<PERSON><PERSON><PERSON> di sicurezza", "securityRolesDefaultLabel": "Scegli ruolo di sicurezza...", "noRolesAssociated": "<PERSON><PERSON><PERSON> ruolo associato", "sendEmailPermissions": "Permessi invio mail", "ignoreCreationEmail": "Ignora creazione email", "ignoreEnrichmentEmail": "Ignora modifica email", "ignoreApprovalEmail": "Ignora approvazione email", "accountsInfo": "Informazioni degli account", "accounts": {"displayName": "Nome utente", "login": "<PERSON><PERSON>", "email": "Email", "status": "Stato", "emptyMessage": "Nessun account collegato a questo gruppo"}, "buttons": {"cancel": "Can<PERSON><PERSON>", "create": "<PERSON><PERSON>", "save": "<PERSON><PERSON>"}}, "createConfirmHeader": "Il gruppo \"{{name}}\" sarà creato", "createConfirmMessage": "Vuoi procedere?", "editConfirmHeader": "Le informazioni modificate per il gruppo \"{{name}}\" ({{id}}) saranno salvate.", "editConfirmMessage": "Vuoi procedere?"}, "workflowProcessesMonitor": {"list": {"table": {"columns": {"client": "Cliente", "processCode": "Codice processo", "requester": "<PERSON><PERSON><PERSON>", "requestType": "Tipo di richiesta", "approver": "Approvatore", "processStatus": "Stato del processo", "bulkProcessCode": "Codice processo collettivo", "materialCode": "Codice Materiale", "plantKey": "Chiave Impianto", "shortDescription": "Breve Descrizione", "requestDate": "Data di richiesta", "approvalDate": "Ultima data di approvazione", "requesterNote": "Nota del richiedente", "approverNote": "Nota dell'approvatore", "completeness": "Completezza", "erpStatus": "Stato ERP", "erpIntegrationStatus": "Ultimo stato di integrazione ERP", "materialType": "Tipo di materiale", "actions": "Azioni", "lastERPOutcome": "", "lastApprovalDate": "", "processEndDate": "", "allNotes": "", "isOutOfSLA": ""}}}}, "massiveRelationship": {"status": {"A_RELATIONSHIP_STARTING": "<PERSON><PERSON><PERSON>", "A_RELATIONSHIP_FORMAL_FILE_VALIDATION": "Convalida formale del file", "A_RELATIONSHIP_EXTRACTING_DATA": "Estrazione dei dati", "A_RELATIONSHIP_VALIDATING_DATA": "Convalida dei dati", "H_RELATIONSHIP_CONFIRM_DATA": "Utente - Conferma modifiche", "A_RELATIONSHIP_PROCESS_GENERATION": "Generazione processi", "H_RELATIONSHIP_COMPLETED": "Completato", "H_RELATIONSHIP_ABANDONED": "Abbandonato", "H_RELATIONSHIP_TERMINATED": "Terminato", "END": "Fine", "A_STARTING": "Vai", "A_FORMAL_FILE_VALIDATION": "Validazione formale File", "A_EXTRACTING_DATA": "Estrazione Dati", "A_VALIDATING_DATA": "Validazione Dati", "H_CONFIRM_DATA": "Verifica e conferma dati", "H_COMPLETED": "Completo", "H_ABANDONED": "Abbandonato", "H_TERMINATED": "Terminato"}, "fieldStatus": {"IGNORED": "<PERSON><PERSON><PERSON>", "CONFIRMED": "Confermato", "INVALID": "Invalido", "NOT_CHANGED": "Non modificato", "NOT_EDITABLE": "Non modificabile", "VALIDATED": "Validato", "DRAFT": "<PERSON><PERSON>"}, "list": {"searchForm": {"actions": {"label": "<PERSON><PERSON><PERSON><PERSON>", "toggleAutorefresh": "Auto-Aggiorna", "startProcess": "Carica", "search": "Cerca", "toggleAutorefresh_false": "Attiva Auto Aggiorna", "toggleAutorefresh_true": "Disattiva Auto Aggiorna ({{interval}})", "downloadTemplate": "Scarica Template"}, "dateFilter": {"label": "Data Caricamento", "placeholder": "Seleziona range di date"}, "status": {"label": "Stato", "placeholder": "Seleziona Stato"}, "user": {"label": "Carica<PERSON> da", "placeholder": "Seleziona utente"}}, "table": {"columns": {"processId": "Id", "uploadedBy": "Car. Da", "uploadDate": "Data. Car.", "filename": "Nome File", "processSummary": "<PERSON><PERSON><PERSON>", "currentStatus": "Stato", "groups": "N. Groups", "actions": " ", "worklist-ref": "ID processo Massivo"}}, "modals": {"startProcessModal": {"header": "Inizia nuovo processo...", "btnConfirm": "Conferma"}, "processCreatedDialog": {"successMsg": "Process {{processId}} creato con successo!", "btnClose": "<PERSON><PERSON>", "btnOpenProcess": "<PERSON>i"}}}, "processDetail": {"actions": {"show_history": "Storico", "toggle_more_info": "<PERSON><PERSON> informazioni", "backToList": "Ritorna a lista processi", "export": "Esporta", "abandon": "<PERSON><PERSON><PERSON><PERSON>", "reload": "Aggiorna", "reload_auto": "Aggiorna ({{countdown}})", "save": "<PERSON><PERSON>", "next": "Prossimo", "replay": "Riproduzione", "set_error": "<PERSON><PERSON><PERSON> errore", "H_RELATIONSHIP_CONFIRM_DATA": {"next": "Avvia Proc.", "next_clear": "Avvia processi di relazione", "next_dirty": "In attesa che l'utente salvi i processi"}, "showAll": "<PERSON><PERSON> tutti", "H_CONFIRM_DATA": {"next": "In. Proc.", "next_clear": "Inizia Processi di Relazionamento", "next_dirty": "In attesa del salvataggio manuale dell'utente"}}, "processInfo": {"labels": {"processId": "<PERSON><PERSON>", "uploadedBy": "Carica<PERSON> da", "uploadDate": "Data Caricamento", "file_name": "Nome File", "currentStatus": "Stato Attuale", "groups": "Groups", "bulkProcessId": "ID processo Massivo"}, "history": {"active": "Attivo", "tmsStart": "Iniziato", "tmsEnd": "<PERSON><PERSON>", "tms_insert": "Data Inserimento", "user_insert": "<PERSON>ser<PERSON>", "tms_update": "Data Aggiornamento", "user_update": "Aggiornato Da", "title": "Storia", "show-all-substate": "Mostra tutti i sottostati"}}, "steps": {"generic": {"default-message-text": "In attesa che i processi automatici aggiornino i dati..."}, "H_RELATIONSHIP_CONFIRM_DATA": {"labels": {"masterdata_header": "Masterdata da aggiornare", "masterdataId": "Tam ID", "client_code": "Mandante / Codeice", "masterdata_header_missing_materials": "I Master Data mancanti non hanno modifiche", "groupTitle": "ID gruppo: {{group}} - {{type}}", "groupInformation": "{{client}}/{{code}} - {{description}}"}, "groupDetails": {"title": "Group ID: {{group}} - Tipo di relazione: {{type}}", "noItemsSelected": "Nessun elemento selezionato", "primaryMaterial": "Materiale primario", "secondaryMaterials": "Materiali secondari", "statusChange": "Cambio di stato", "comment": "Commento", "note": "<PERSON>a", "description": "Descrizione", "changes": "Modifiche", "materialCode": "Codice materiale", "status": "Stato", "changesValue": "{{fieldName}}: {{oldValue}}->{{newValue}}", "materials": "Materiali", "goldenRecord": {"materialInstances": "Istanze Materiale", "materialDetails": "Dettagli del materiale"}, "syncInstancesMessage": "Queste informazioni verranno utilizzate per la sincronizzazione delle istanze dopo la creazione del Golden-Record.", "invalidGroup": "Il gruppo non è valido"}}, "H_RELATIONSHIP_COMPLETED": {"rowExpand": {"primary": "Primario", "secondary": "Secondario"}}, "A_RELATIONSHIP_PROCESS_GENERATION": {"waiting-message": "In attesa che i processi di relazionamento vengano generati..."}, "END": {"H_RELATIONSHIP_TERMINATED_message": "Processo terminato automaticamente! Si è verificato un errore critico.", "H_RELATIONSHIP_COMPLETED_message": "Processo completato!", "H_RELATIONSHIP_ABANDONED_message": "Processo abbandonato dall'utente", "table": {"header": {"materialGroup": "ID gruppo", "materialDescr": "Descrizione", "processCode": "Esito", "status": "Stato", "type": "Tipo"}, "messages": {"process-not-generated": "Nessun processo generato!", "process-generated": "Processo <b>{{materialProcessId}}</b> lanciato"}}, "H_TERMINATED_message": "Processo terminato automaticamente! Some critical error occurred.", "H_COMPLETED_message": "Processo completato!", "H_ABANDONED_message": "Processo abbandonato dall'utente"}}, "actionBar": {"abandonDialog": {"header": "<PERSON>fer<PERSON> abbandono", "message": "Sei sicuro di voler abbandonare il processo?", "acceptBtn": "Conferma", "rejectBtn": "<PERSON><PERSON><PERSON>"}}}, "errors": {"extraction-purpose-metadata-mandatory": "Il file caricato non è valido. Esporta un massive relationship template", "language-metadata-mandatory": "Il metadato della lingua è obbligatorio.", "customer-id-metadata-mandatory": "Il metadato del customer id è obbligatorio.", "unable-to-read-input-file": "Impossibile leggere il file di input.", "input-file-mandatory": "Il file Excel di input è obbligatorio.", "user-id-mandatory": "L'ID Utente è obbligatorio.", "language-mandatory": "La lingua corrente è obbligatoria.", "validation-error": "Errore di validazione"}, "messages": {"title": {"extraction-purpose-error": "Tipo di esportazione non valido", "extraction-purpose-success": "Tipo di espotazione", "language-error": "Lingua sbagliata", "language-success": "<PERSON><PERSON>", "customer-id-error": "Identificativo cliente errato", "customer-id-success": "Identificativo cliente", "sheet-number-error": "Numero fogli errato", "sheet-number-success": "Numero fogli", "wrong-column-position-error": "Posizione colonna errata", "wrong-column-position-success": "Posizione colonna"}, "body": {"extraction-purpose-error": "Il template di esportazione non è valido", "extraction-purpose-success": "Il template di esportazione è valido", "language-error": "La lingua non è valida", "language-success": "La lingua è valida", "customer-id-error": "L'identificativo cliente non è valido", "customer-id-success": "L'identificativo cliente è valido", "sheet-number-error": "Il file contiene un numero di fogli errato.", "sheet-number-success": "Il file contiene un numero di fogli corretto.", "wrong-column-position-error": "Una colonna è nella posizione sbagliata.", "wrong-column-position-success": "<PERSON>tte le colonne sono al posto giusto."}}, "internalStatus": {"PENDING": "IN ATTESA", "WORKING": "IN LAVORAZIONE", "COMPLETED": "COMPLETATA", "ERROR": "ERRORE"}, "validatingData": {"multipleLeadingMasters": "Più di un master principale trovato nel gruppo '{{group}}'.", "noPrimaryRecord": "Il master principale non è presente per il gruppo '{{group}}'.", "multipleRelationshipTypes": "Sono stati trovati più tipi di relazione nel gruppo '{{group}}'.", "inconsistentMaterialCode": "Codice materiale incoerente per il materiale nel gruppo '{{group}}'.", "fewerRecords": "Il numero di record è inferiore a 2 per il gruppo '{{group}}'.", "clientCodeMissing": "Il codice cliente non è presente per il materiale con codice '{{code}}' nel gruppo '{{group}}'.", "materialCodeMissing": "Il codice materiale non è presente per il materiale con cliente '{{client}}' nel gruppo '{{group}}'.", "relationshipTypeMissing": "Il tipo di relazione non è presente per il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}'.", "statusPrefixInvalid": "Il prefisso dello stato non corrisponde al cliente per il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}'.", "materialInOtherGroups": "Il materiale con cliente '{{client}}' e codice '{{code}}' è già presente in un altro gruppo con lo stesso tipo di relazione.", "noDataForGroup": "<PERSON><PERSON><PERSON> dato trovato per il gruppo", "hasNotAuthorization": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' non ha l'autorizzazione corretta per creare una relazione.", "materialNotFound": "Materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' non trovato.", "materialDeleted": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' è stato eliminato.", "materialInRelationship": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' è già presente in una relazione di Golden Record.", "materialInAnotherProcess": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' è già presente in un altro processo di relazione.", "materialDuplicate": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' è già presente in un altro processo di relazione come duplicato o Golden Record.", "materialInvalidGeneric": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' era non valido a causa di errori.", "materialInvalid": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' era non valido a causa di errori: {{errors}}.", "materialError": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' ha generato un errore {{error}}.", "relationshipCrossClient": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' non è abilitato per la relazione Cross-Client.", "materialClassificationInvalid": "Il materiale con cliente '{{client}}' e codice '{{code}}' nel gruppo '{{group}}' ha una classificazione non valida.", "genericError": "Si è verificato un errore imprevisto.", "relationshipAuthorizationTitle": "Autorizzazione Materiale.", "materialNotFoundTitle": "Materiale non trovato.", "materialStatusTitle": "Stato del materiale.", "relationshipErrorTitle": "Errore di relazione.", "materialErrorTitle": "Errore materiale.", "prevalidationErrorTitle": "Errore di Prevalidazione.", "materialNotFoundBulkValidationMessage": "Materiale con cliente '{{client}}' e codice '{{code}}' per il processo: '{{process}}' non è stato trovato.", "materialNotFoundBulkValidationTitle": "Materiale non trovato.", "genericProcessErrorMessage": "Il processo '{{process}}' ha generato un errore.", "processValidatedSuccessMessage": "Processo '{{process}}' convalidato con successo.", "genericTitle": "Dati di convalida", "noGroupsFound": "Nessun gruppo trovato nel processo"}, "extractingData": {"noLeadingMasterFound": "Nessun master principale trovato.", "rowsEmpty": "L'elenco delle righe analizzate non può essere nullo.", "materialDetailsListNull": "L'elenco dei dettagli dei materiali non può essere nullo.", "goldenRecordDetailsError": "Impossibile recuperare i dettagli del Golden Record", "invalidMaterialStatus": "Formato non valido. Formato previsto: <cliente>_<statoMateriale> - <descrizione>", "invalidCodeDescription": "Formato non valido. Formato previsto: <codice>_<descrizione>", "tooManyGroupsUploaded": "Ci sono troppi gruppi nel file caricato."}}, "organization-management": {"createConfirmHeader": "L'organizzazione \"{{name}}\" sarà creata", "createConfirmMessage": "Vuoi procedere?", "editConfirmHeader": "Le informazioni modificate dell'organizzazione per \"{{name}}\" ({{id}}) verranno salvate.", "editConfirmMessage": "Vuoi procedere?", "deleteConfirmationHeader": "Elimina conferma", "deleteConfirmationMessage": "Vuoi eliminare le organizzazioni selezionate?", "list": {"pageTitle": "Gestione delle organizzazioni", "searchPlaceholder": "Cerca per nome…", "organizationName": "Nome dell'organizzazione", "linkedAccount": "Account collegati", "linkedRole": "<PERSON><PERSON><PERSON> collegati"}, "filters": {"addNew": "Nuovo", "actions": {"title": "Azioni", "delete": "Elimina"}, "export": "Esporta"}, "edit": {"id": "Id", "name": "Nome", "flgActive": "Attivo", "namePlaceholder": "Nome dell'organizzazione", "buttons": {"cancel": "<PERSON><PERSON>", "create": "<PERSON><PERSON>", "save": "<PERSON><PERSON>"}}}, "ontology": {"errors": {"cannot-specify-bin-without-warehouse": "", "warehouses-bins-must-have-same-size": ""}}, "integrationMonitor": {"buttons": {"search": "", "generateExcel": ""}, "filters": {"processTypes": "", "processStatuses": "", "eventIds": "", "processCodes": "", "clients": "", "dateFrom": "", "dateTo": ""}, "list": {"table": {"columns": {"eventId": "", "processType": "", "status": "", "lastUpdated": "", "processCode": "", "reason": "", "actions": "Azioni"}}}, "actions": {"download-event": "Scarica l'evento", "reset-status": "Reimposta lo stato"}, "messages": {"resetConfirmTitle": "Confermare l'operazione.", "resetConfirmMessage": "Sei sicuro di reimpostare lo stato da 'PENDING' a 'NEW' per l'evento [{{eventId}}]?", "resetOkTitle": "Operazione completata con successo.", "resetOkMessage": "Reimpostazione dello stato per l'evento selezionato completata con successo."}}, "relationship-service": {"errors": {"relationship-already-requested": "", "relationship-already-exists": ""}}, "deduplication": {"relationshipType": {"selectedSubtype": "Seleziona un sottotipo", "fieldRequired": "Seleziona i campi richiesti", "subtypeRequired": "Seleziona i campi del sottotipo"}, "materialSelection": {"minMaterials": "Seleziona almeno 2 materiali"}, "materialMasterDataStatus": {"masterDataStatus": "Seleziona lo stato per ogni materiale secondario"}, "enrichment": {"title": "Arricchimento Materiale", "loading": "Caricamento dati di arricchimento...", "noData": "<PERSON><PERSON><PERSON> dato di arricchimento disponibile", "clientPagination": "Cliente", "client": "Cliente", "noClient": "NESSUN CLIENTE", "showEnriched": "<PERSON><PERSON>", "hideNonEditables": "Nascondi Non Modificabili"}, "steps": {"button": {"back": "Indietro", "next": "<PERSON><PERSON>", "confirm": "Conferma"}, "materialStatus": "Seleziona stato materiale", "materialEnrich": "Arricchisci materiale", "plantEnrich": "Arricchi<PERSON> impianto", "summary": {"header": "Riepilogo", "tableColumn": {"relationshipType": "Tipo di relazione", "subtype": "Sottotipo", "plantCode": "Codice impianto", "plantDescription": "Descrizione impianto", "plantChangeType": "Tipo di modifica"}, "plantChangesTitle": "Modifiche impianto"}}, "confirmModal": {"close": "<PERSON><PERSON>", "confirm": "Conferma relazione"}, "validation": {"selectRelationshipType": "Seleziona un tipo di relazione", "selectMinMaterials": "Seleziona almeno 2 materiali", "genericError": "Errore di validazione"}, "common": {"loading": "Caricamento...", "notAvailable": "N/D"}}}