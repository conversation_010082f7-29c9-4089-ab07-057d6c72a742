import { Component, Input, OnInit } from "@angular/core";
import { SelectorMap } from "@creactives/models";
import { Tam4TranslationService } from "@creactives/tam4-translation-core";
import { TranslateService, TranslateModule } from "@ngx-translate/core";
import {
  Tam4SelectorConfig,
  TamAbstractReduxComponent,
} from "src/app/components";
import { ComponentsModule } from "src/app/modules/components/components.module";
import { LayoutModule } from "src/app/modules/layout/layout.module";
import {
  GetAvailableClientsRequest,
  GetOriginalMaterialRequest,
  IPlantExtensionDetail,
} from "src/app/modules/layout/store/actions/popup.actions";
import { DeduplicationState } from "../../../store/deduplication.state";
import { Store } from "@ngrx/store";
import { DeduplicationSelectors } from "../../../store/deduplication.selectors";
import { UntypedFormBuilder, UntypedFormGroup } from "@angular/forms";
import { getPlantExtensionDetail } from "src/app/modules/layout/store/reducers";
import { DeduplicationSubStepContainerComponent } from "../../deduplication-sub-step-container.component";

const storeSelectors: Tam4SelectorConfig[] = [
  {
    key: "deduplicationRequest",
    selector: DeduplicationSelectors.getDeduplicationRequest,
  },
];

@Component({
  selector: 'relationship-plant-data-enrich',
  template: `{{'deduplication.steps.plantEnrich' | translate}}`,
  standalone: true,
  imports: [TranslateModule]
})
export class RelationshipPlantDataEnrich
  extends TamAbstractReduxComponent<SelectorMap>
  implements OnInit
{
  @Input() mdDomain!: string;

  formGroup: UntypedFormGroup;
  submitted = false;
  plantsToExtension: IPlantExtensionDetail[];

  constructor(
    protected translate: TranslateService,
    protected tamTranslate: Tam4TranslationService,
    protected store: Store<DeduplicationState>,
    private formBuilder: UntypedFormBuilder
  ) {
    super(translate, tamTranslate, store, storeSelectors);
    this.formGroup = this.formBuilder.group(
      {
        plantDescription: [{ value: "", disabled: true }],
        client: [{ value: "", disabled: true }],
        plantCode: [{ value: "", disabled: true }],
        plantClient: [{ value: "", disabled: true }],
      },
      {
        updateOn: "submit",
      }
    );
  }

  ngOnInit(): void {
    // this.store.dispatch(
    //   new GetAvailableClientsRequest({
    //     materialId:
    //       this.signals?.deduplicationRequest().materialsDeduplication[0]
    //         .materialId,
    //   })
    // );
    // this.store.dispatch(
    //   new GetOriginalMaterialRequest({
    //     materialId:
    //       this.signals?.deduplicationRequest().materialsDeduplication[0]
    //         .materialId,
    //   })
    // );

    // this.store.select(getPlantExtensionDetail)
    //       .subscribe(plantsToExtension => this.plantsToExtension = plantsToExtension)
  }

  updateValueInStore(event$: any) {}
}
