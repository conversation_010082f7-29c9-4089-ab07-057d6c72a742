import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { Subscription } from "rxjs";
import { DeduplicationCommunicationService } from "../services/deduplication-communication.service";
import { CommonModule } from "@angular/common";
import { RelationshipMaterialEnrich } from "./wizard/steps/relationship-material-enrich.component";
import { RelationshipPlantDataEnrich } from "./wizard/steps/relationship-plant-data-enrich.component";
import { Tam4SelectorConfig, TamAbstractReduxComponent } from "src/app/components";
import { SelectorMap } from "@creactives/models";
import { TranslateService } from "@ngx-translate/core";
import { Tam4TranslationService } from "@creactives/tam4-translation-core";
import { DeduplicationState } from "../store/deduplication.state";
import { Store } from "@ngrx/store";
import { DeduplicationSelectors } from "../store/deduplication.selectors";
import { DEDUPLICATION_ACTIONS } from "../store/deduplication.actions";

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'groupClients', selector: DeduplicationSelectors.getGroupClients},
  {key: 'currentClient', selector: DeduplicationSelectors.getCurrentClient}
];

@Component({
  selector: 'deduplication-sub-step-container',
  template: `
  <div [ngSwitch]="stepType">

    <relationship-material-enrich
    *ngSwitchCase="'relationship-material-enrich'"
    [mdDomain]="mdDomain"
    [currentClient]="'signals?.currentClient()'">
    </relationship-material-enrich>

    <relationship-plant-data-enrich
    *ngSwitchCase="'relationship-plant-data-enrich'"
    [mdDomain]="mdDomain"
    [currentClient]="'signals?.currentClient()'">
    </relationship-plant-data-enrich>
  </div>
  `,
  standalone: true,
  imports: [ 
    CommonModule,
    RelationshipMaterialEnrich,
    RelationshipPlantDataEnrich
]
})
export class DeduplicationSubStepContainerComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit, OnDestroy{
    @Input() mdDomain!: string;
    @Input() stepType!: string;
    
    subscription = new Subscription();

    currentClientIndex = 0;
    callCount = 0;
    max = 3;
    min = 1;


    constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store<DeduplicationState>,
              private commService: DeduplicationCommunicationService
            ) {
    super(translate, tamTranslate, store, storeSelectors);
              }
    ngOnInit(): void {
        // Initialize current client index based on the current client in store
        const groupClients = this.signals?.groupClients();
        const currentClient = this.signals?.currentClient();
        if (groupClients && currentClient) {
            this.currentClientIndex = groupClients.indexOf(currentClient);
        }

        const triggerNextSub = this.commService.triggerNextCall$.subscribe(() => {
        this.callNext();
        });

        // const triggerBackSub = this.commService.triggerBackCall$.subscribe(() => {
        // this.callBack();
        // });

        this.subscription.add(triggerNextSub);
        // this.subscription.add(triggerBackSub);
    }

    callNext(): void {
        const groupClients = this.signals?.groupClients();
        if (!groupClients || groupClients.length === 0) {
            return;
        }

        // Check if we're on the last client
        if (this.currentClientIndex >= groupClients.length - 1) {
            // We've processed all clients, notify to advance to next stepper step
            this.commService.notifyStopConditionReached();
            return;
        }

        // Move to next client
        this.currentClientIndex++;
        const nextClient = groupClients[this.currentClientIndex];

        console.log(`Moving to client ${this.currentClientIndex + 1}/${groupClients.length}: ${nextClient}`);

        // Update current client in store - this will trigger data reload in the component
        this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_CURRENT_CLIENT(nextClient));

        // Check if this is now the last client
        if (this.currentClientIndex >= groupClients.length - 1) {
            this.commService.notifyStopConditionReached();
        }
    }

    // callBack(): void {
    //     if (this.callCount === this.min) {
    //     this.commService.notifyStartConditionReached();
    //     } else {
    //     this.callCount--;
    //     }
    // }

      ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

}